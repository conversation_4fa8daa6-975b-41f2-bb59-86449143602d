#!/usr/bin/env python3
"""
测试脚本 - 验证FastMCP Streamable HTTP服务器功能
"""

import asyncio
import sys
from fastmcp import Client

async def test_basic_server():
    """测试基础模板服务器"""
    print("🧪 Testing Basic Template Server (Port 8000)")
    print("=" * 50)
    
    server_url = "http://127.0.0.1:8000/mcp/"
    
    try:
        async with Client(server_url) as client:
            # 测试连接
            await client.ping()
            print("✅ Server connection successful")
            
            # 测试工具
            print("\n🛠️  Testing tools:")
            
            # 测试时间工具
            result = await client.call_tool("get_current_time")
            print(f"  ⏰ Current time: {result[0].text}")
            
            # 测试计算工具
            result = await client.call_tool("calculate_sum", {"numbers": [1, 2, 3, 4, 5]})
            print(f"  🔢 Sum calculation: {result[0].text}")
            
            # 测试文本格式化
            result = await client.call_tool("format_text", {"text": "hello world", "style": "title"})
            print(f"  📝 Text formatting: {result[0].text}")
            
            # 测试资源
            print("\n📚 Testing resources:")
            config = await client.read_resource("config://server")
            print(f"  ⚙️  Server config: Available")
            
            print("✅ Basic Template Server test completed successfully!\n")
            return True
            
    except Exception as e:
        print(f"❌ Basic Template Server test failed: {e}\n")
        return False

async def test_api_conversion_server():
    """测试API转换示例服务器"""
    print("🧪 Testing API Conversion Server (Port 8001)")
    print("=" * 50)
    
    server_url = "http://127.0.0.1:8001/mcp/"
    
    try:
        async with Client(server_url) as client:
            # 测试连接
            await client.ping()
            print("✅ Server connection successful")
            
            # 测试用户管理API
            print("\n👤 Testing User Management:")
            result = await client.call_tool("get_user_info", {"user_id": 1})
            print(f"  📋 Get user info: Success")
            
            result = await client.call_tool("create_user", {
                "name": "测试用户",
                "email": "<EMAIL>"
            })
            print(f"  ➕ Create user: Success")
            
            # 测试产品管理API
            print("\n📦 Testing Product Management:")
            result = await client.call_tool("query_products", {"category": "电子产品"})
            print(f"  🔍 Query products: Success")
            
            result = await client.call_tool("update_product_stock", {
                "product_id": 1,
                "new_stock": 15
            })
            print(f"  📊 Update stock: Success")
            
            # 测试文件操作API
            print("\n📁 Testing File Operations:")
            result = await client.call_tool("list_files", {"directory": "."})
            print(f"  📂 List files: Success")
            
            # 测试外部服务API
            print("\n🌐 Testing External Services:")
            result = await client.call_tool("get_weather", {"city": "北京"})
            print(f"  🌤️  Get weather: Success (simulated)")
            
            result = await client.call_tool("translate_text", {
                "text": "你好",
                "target_language": "en"
            })
            print(f"  🔤 Translate text: Success (simulated)")
            
            # 测试资源
            print("\n📚 Testing Resources:")
            docs = await client.read_resource("api://documentation")
            print(f"  📖 API documentation: Available")
            
            print("✅ API Conversion Server test completed successfully!\n")
            return True
            
    except Exception as e:
        print(f"❌ API Conversion Server test failed: {e}\n")
        return False

async def run_comprehensive_test():
    """运行综合测试"""
    print("🚀 FastMCP Streamable HTTP Servers Comprehensive Test")
    print("=" * 60)
    print()
    
    # 测试基础服务器
    basic_success = await test_basic_server()
    
    # 测试API转换服务器
    api_success = await test_api_conversion_server()
    
    # 总结
    print("📊 Test Summary")
    print("=" * 20)
    print(f"Basic Template Server: {'✅ PASS' if basic_success else '❌ FAIL'}")
    print(f"API Conversion Server: {'✅ PASS' if api_success else '❌ FAIL'}")
    
    if basic_success and api_success:
        print("\n🎉 All tests passed! Your FastMCP servers are working correctly.")
        return 0
    else:
        print("\n⚠️  Some tests failed. Please check the server status and try again.")
        return 1

async def test_single_server(port: int):
    """测试单个服务器"""
    server_url = f"http://127.0.0.1:{port}/mcp/"
    server_name = "Basic Template" if port == 8000 else "API Conversion"
    
    print(f"🧪 Testing {server_name} Server (Port {port})")
    print("=" * 40)
    
    try:
        async with Client(server_url) as client:
            await client.ping()
            print("✅ Server is running and responsive")
            
            # 列出工具
            tools = await client.list_tools()
            print(f"📋 Available tools: {len(tools)}")
            for tool in tools[:3]:  # 只显示前3个
                print(f"  - {tool.name}")
            if len(tools) > 3:
                print(f"  ... and {len(tools) - 3} more")
            
            # 列出资源
            resources = await client.list_resources()
            print(f"📚 Available resources: {len(resources)}")
            for resource in resources[:3]:  # 只显示前3个
                print(f"  - {resource.uri}")
            if len(resources) > 3:
                print(f"  ... and {len(resources) - 3} more")
            
            print(f"✅ {server_name} Server is working correctly!\n")
            return True
            
    except Exception as e:
        print(f"❌ {server_name} Server test failed: {e}")
        print("💡 Make sure the server is running before testing.\n")
        return False

def main():
    """主函数"""
    if len(sys.argv) > 1:
        if sys.argv[1] == "basic":
            # 测试基础服务器
            success = asyncio.run(test_single_server(8000))
            sys.exit(0 if success else 1)
        elif sys.argv[1] == "api":
            # 测试API转换服务器
            success = asyncio.run(test_single_server(8001))
            sys.exit(0 if success else 1)
        elif sys.argv[1] == "quick":
            # 快速测试两个服务器
            async def quick_test():
                basic = await test_single_server(8000)
                api = await test_single_server(8001)
                return basic and api
            
            success = asyncio.run(quick_test())
            sys.exit(0 if success else 1)
        else:
            print("Usage:")
            print("  python test_servers.py          # Run comprehensive tests")
            print("  python test_servers.py basic    # Test basic server only")
            print("  python test_servers.py api      # Test API conversion server only")
            print("  python test_servers.py quick    # Quick test both servers")
            sys.exit(1)
    else:
        # 运行综合测试
        exit_code = asyncio.run(run_comprehensive_test())
        sys.exit(exit_code)

if __name__ == "__main__":
    main()
