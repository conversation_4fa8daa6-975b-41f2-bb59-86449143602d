#!/usr/bin/env python3
"""
测试脚本 - 验证FastMCP Streamable HTTP服务器功能
"""

import asyncio
import sys
import subprocess
import time
import signal
import os
from fastmcp import Client

class ServerManager:
    """管理MCP服务器进程"""

    def __init__(self):
        self.processes = {}

    def is_port_in_use(self, port: int) -> bool:
        """检查端口是否被占用"""
        try:
            import socket
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(1)
                result = s.connect_ex(('127.0.0.1', port))
                return result == 0
        except:
            return False

    def kill_port_process(self, port: int):
        """杀死占用端口的进程"""
        try:
            result = subprocess.run(['lsof', '-ti', f':{port}'],
                                  capture_output=True, text=True)
            if result.returncode == 0 and result.stdout.strip():
                pids = result.stdout.strip().split('\n')
                for pid in pids:
                    try:
                        subprocess.run(['kill', '-9', pid], check=True)
                        print(f"🔪 Killed process {pid} on port {port}")
                    except:
                        pass
        except:
            pass

    def start_server(self, script_name: str, port: int, timeout: int = 15):
        """启动服务器"""
        print(f"🚀 Starting {script_name} on port {port}...")

        # 检查端口是否被占用
        if self.is_port_in_use(port):
            print(f"⚠️  Port {port} is in use, attempting to free it...")
            self.kill_port_process(port)
            time.sleep(2)  # 等待进程完全退出

            if self.is_port_in_use(port):
                print(f"❌ Port {port} is still in use after cleanup attempt")
                return False

        try:
            # 启动服务器进程
            process = subprocess.Popen(
                [sys.executable, script_name],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            self.processes[port] = process

            # 等待服务器启动
            print(f"⏳ Waiting for server to start (timeout: {timeout}s)...")
            for i in range(timeout):
                if process.poll() is not None:
                    # 进程已退出，检查错误
                    stdout, stderr = process.communicate()
                    print(f"❌ Server failed to start:")
                    if stderr:
                        print(f"Error: {stderr}")
                    if stdout:
                        print(f"Output: {stdout}")
                    return False

                # 检查服务器是否响应
                try:
                    import httpx
                    # 尝试连接到MCP端点
                    response = httpx.get(f"http://127.0.0.1:{port}/mcp/", timeout=3.0)
                    if response.status_code in [200, 404, 405]:  # 405 Method Not Allowed也表示服务器在运行
                        print(f"✅ Server started successfully on port {port}")
                        return True
                except httpx.ConnectError:
                    # 连接被拒绝，服务器还没准备好
                    pass
                except httpx.TimeoutException:
                    # 超时，服务器可能还在启动
                    pass
                except Exception as e:
                    # 其他错误，可能是服务器正在启动
                    if i > 5:  # 5秒后开始显示详细错误
                        print(f"  Connection attempt failed: {type(e).__name__}")
                    pass

                time.sleep(1)
                if i < 3:  # 只在前几次显示等待信息
                    print(f"  Waiting... ({i+1}/{timeout})")

            print(f"⚠️  Server startup timeout after {timeout}s")
            return False

        except Exception as e:
            print(f"❌ Failed to start server: {e}")
            return False

    def stop_server(self, port: int):
        """停止服务器"""
        if port in self.processes:
            process = self.processes[port]
            if process.poll() is None:  # 进程仍在运行
                print(f"🛑 Stopping server on port {port}...")
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                    process.wait()
            del self.processes[port]

    def stop_all(self):
        """停止所有服务器"""
        for port in list(self.processes.keys()):
            self.stop_server(port)

# 全局服务器管理器
server_manager = ServerManager()

def cleanup_servers():
    """清理服务器进程"""
    server_manager.stop_all()

# 注册清理函数
import atexit
atexit.register(cleanup_servers)

async def test_basic_server():
    """测试基础模板服务器"""
    print("🧪 Testing Basic Template Server (Port 8000)")
    print("=" * 50)
    
    server_url = "http://127.0.0.1:8000/mcp/"
    
    try:
        async with Client(server_url) as client:
            # 测试连接
            await client.ping()
            print("✅ Server connection successful")
            
            # 测试工具
            print("\n🛠️  Testing tools:")
            
            # 测试时间工具
            result = await client.call_tool("get_current_time")
            print(f"  ⏰ Current time: {result[0].text}")
            
            # 测试计算工具
            result = await client.call_tool("calculate_sum", {"numbers": [1, 2, 3, 4, 5]})
            print(f"  🔢 Sum calculation: {result[0].text}")
            
            # 测试文本格式化
            result = await client.call_tool("format_text", {"text": "hello world", "style": "title"})
            print(f"  📝 Text formatting: {result[0].text}")
            
            # 测试资源
            print("\n📚 Testing resources:")
            config = await client.read_resource("config://server")
            print(f"  ⚙️  Server config: Available")
            
            print("✅ Basic Template Server test completed successfully!\n")
            return True
            
    except Exception as e:
        print(f"❌ Basic Template Server test failed: {e}\n")
        return False

async def test_api_conversion_server():
    """测试API转换示例服务器"""
    print("🧪 Testing API Conversion Server (Port 8001)")
    print("=" * 50)
    
    server_url = "http://127.0.0.1:8001/mcp/"
    
    try:
        async with Client(server_url) as client:
            # 测试连接
            await client.ping()
            print("✅ Server connection successful")
            
            # 测试用户管理API
            print("\n👤 Testing User Management:")
            result = await client.call_tool("get_user_info", {"user_id": 1})
            print(f"  📋 Get user info: Success")
            
            result = await client.call_tool("create_user", {
                "name": "测试用户",
                "email": "<EMAIL>"
            })
            print(f"  ➕ Create user: Success")
            
            # 测试产品管理API
            print("\n📦 Testing Product Management:")
            result = await client.call_tool("query_products", {"category": "电子产品"})
            print(f"  🔍 Query products: Success")
            
            result = await client.call_tool("update_product_stock", {
                "product_id": 1,
                "new_stock": 15
            })
            print(f"  📊 Update stock: Success")
            
            # 测试文件操作API
            print("\n📁 Testing File Operations:")
            result = await client.call_tool("list_files", {"directory": "."})
            print(f"  📂 List files: Success")
            
            # 测试外部服务API
            print("\n🌐 Testing External Services:")
            result = await client.call_tool("get_weather", {"city": "北京"})
            print(f"  🌤️  Get weather: Success (simulated)")
            
            result = await client.call_tool("translate_text", {
                "text": "你好",
                "target_language": "en"
            })
            print(f"  🔤 Translate text: Success (simulated)")
            
            # 测试资源
            print("\n📚 Testing Resources:")
            docs = await client.read_resource("api://documentation")
            print(f"  📖 API documentation: Available")
            
            print("✅ API Conversion Server test completed successfully!\n")
            return True
            
    except Exception as e:
        print(f"❌ API Conversion Server test failed: {e}\n")
        return False

async def run_comprehensive_test():
    """运行综合测试"""
    print("🚀 FastMCP Streamable HTTP Servers Comprehensive Test")
    print("=" * 60)
    print()
    
    # 测试基础服务器
    basic_success = await test_basic_server()
    
    # 测试API转换服务器
    api_success = await test_api_conversion_server()
    
    # 总结
    print("📊 Test Summary")
    print("=" * 20)
    print(f"Basic Template Server: {'✅ PASS' if basic_success else '❌ FAIL'}")
    print(f"API Conversion Server: {'✅ PASS' if api_success else '❌ FAIL'}")
    
    if basic_success and api_success:
        print("\n🎉 All tests passed! Your FastMCP servers are working correctly.")
        return 0
    else:
        print("\n⚠️  Some tests failed. Please check the server status and try again.")
        return 1

async def test_single_server(port: int):
    """测试单个服务器"""
    server_url = f"http://127.0.0.1:{port}/mcp/"
    server_name = "Basic Template" if port == 8000 else "API Conversion"
    
    print(f"🧪 Testing {server_name} Server (Port {port})")
    print("=" * 40)
    
    try:
        async with Client(server_url) as client:
            await client.ping()
            print("✅ Server is running and responsive")
            
            # 列出工具
            tools = await client.list_tools()
            print(f"📋 Available tools: {len(tools)}")
            for tool in tools[:3]:  # 只显示前3个
                print(f"  - {tool.name}")
            if len(tools) > 3:
                print(f"  ... and {len(tools) - 3} more")
            
            # 列出资源
            resources = await client.list_resources()
            print(f"📚 Available resources: {len(resources)}")
            for resource in resources[:3]:  # 只显示前3个
                print(f"  - {resource.uri}")
            if len(resources) > 3:
                print(f"  ... and {len(resources) - 3} more")
            
            print(f"✅ {server_name} Server is working correctly!\n")
            return True
            
    except Exception as e:
        print(f"❌ {server_name} Server test failed: {e}")
        print("💡 Make sure the server is running before testing.\n")
        return False

async def test_in_memory():
    """使用内存传输测试服务器 - 推荐方法"""
    print("🧪 Testing FastMCP Servers with In-Memory Transport (Recommended)")
    print("=" * 70)

    # 导入服务器模块
    import streamable_http_mcp_server
    import api_conversion_example

    success_count = 0

    # 测试基础模板服务器
    print("\n🔧 Testing Basic Template Server (In-Memory)")
    print("-" * 50)

    try:
        async with Client(streamable_http_mcp_server.mcp) as client:
            await client.ping()
            print("✅ Server connection successful")

            tools = await client.list_tools()
            print(f"📋 Available tools: {len(tools)}")

            # 测试一个工具
            result = await client.call_tool("get_current_time")
            print(f"⏰ Current time: {result[0].text}")

            resources = await client.list_resources()
            print(f"📚 Available resources: {len(resources)}")

            print("✅ Basic Template Server test completed successfully!")
            success_count += 1

    except Exception as e:
        print(f"❌ Basic Template Server test failed: {e}")

    # 测试API转换服务器
    print("\n🔧 Testing API Conversion Server (In-Memory)")
    print("-" * 50)

    try:
        async with Client(api_conversion_example.mcp) as client:
            await client.ping()
            print("✅ Server connection successful")

            tools = await client.list_tools()
            print(f"📋 Available tools: {len(tools)}")

            # 测试一个工具
            result = await client.call_tool("get_user_info", {"user_id": 1})
            print(f"👤 Get user info: Success")

            resources = await client.list_resources()
            print(f"📚 Available resources: {len(resources)}")

            print("✅ API Conversion Server test completed successfully!")
            success_count += 1

    except Exception as e:
        print(f"❌ API Conversion Server test failed: {e}")

    return success_count == 2

def main():
    """主函数"""
    if len(sys.argv) > 1:
        if sys.argv[1] == "memory" or sys.argv[1] == "inmemory":
            # 内存传输测试 - 推荐方法
            print("🚀 Starting In-Memory Transport Test (Recommended)")
            print("=" * 50)

            success = asyncio.run(test_in_memory())
            sys.exit(0 if success else 1)

        elif sys.argv[1] == "basic":
            # 测试基础服务器
            print("🚀 Starting Basic Template Server Test")
            print("=" * 40)

            if server_manager.start_server("streamable_http_mcp_server.py", 8000):
                success = asyncio.run(test_single_server(8000))
                server_manager.stop_server(8000)
                sys.exit(0 if success else 1)
            else:
                print("❌ Failed to start basic server")
                sys.exit(1)

        elif sys.argv[1] == "api":
            # 测试API转换服务器
            print("🚀 Starting API Conversion Server Test")
            print("=" * 40)

            if server_manager.start_server("api_conversion_example.py", 8001):
                success = asyncio.run(test_single_server(8001))
                server_manager.stop_server(8001)
                sys.exit(0 if success else 1)
            else:
                print("❌ Failed to start API conversion server")
                sys.exit(1)

        elif sys.argv[1] == "quick":
            # 快速测试两个服务器
            print("🚀 Starting Quick Test of Both Servers")
            print("=" * 40)

            basic_started = server_manager.start_server("streamable_http_mcp_server.py", 8000)
            api_started = server_manager.start_server("api_conversion_example.py", 8001)

            if basic_started and api_started:
                async def quick_test():
                    basic = await test_single_server(8000)
                    api = await test_single_server(8001)
                    return basic and api

                success = asyncio.run(quick_test())
                server_manager.stop_all()
                sys.exit(0 if success else 1)
            else:
                print("❌ Failed to start one or both servers")
                server_manager.stop_all()
                sys.exit(1)

        elif sys.argv[1] == "manual":
            # 手动测试模式 - 假设服务器已经在运行
            print("🔧 Manual Test Mode - Assuming servers are already running")
            print("=" * 60)

            async def manual_test():
                basic = await test_single_server(8000)
                api = await test_single_server(8001)
                return basic and api

            success = asyncio.run(manual_test())
            sys.exit(0 if success else 1)

        else:
            print("Usage:")
            print("  python test_servers.py                # Run in-memory tests (recommended)")
            print("  python test_servers.py memory         # Run in-memory tests (recommended)")
            print("  python test_servers.py basic          # Test basic server only (HTTP)")
            print("  python test_servers.py api            # Test API conversion server only (HTTP)")
            print("  python test_servers.py quick          # Quick test both servers (HTTP)")
            print("  python test_servers.py manual         # Test assuming servers are already running")
            print("")
            print("💡 Note: In-memory testing is recommended as it's more reliable.")
            print("💡 HTTP testing may encounter transport-related issues.")
            sys.exit(1)
    else:
        # 默认运行内存传输测试
        print("🚀 Starting Default In-Memory Transport Test")
        print("=" * 50)
        print("💡 Using in-memory transport for reliable testing")
        print("💡 Use 'python test_servers.py quick' for HTTP transport testing")
        print("")

        success = asyncio.run(test_in_memory())

        if success:
            print("\n🎉 All tests passed! Your FastMCP servers are working correctly.")
            print("💡 To test HTTP transport, run: python test_servers.py quick")
            sys.exit(0)
        else:
            print("\n⚠️  Some tests failed. Please check the error messages above.")
            sys.exit(1)

if __name__ == "__main__":
    main()
