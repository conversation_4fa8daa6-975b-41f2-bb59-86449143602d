#!/usr/bin/env python3
"""
简化的Elasticsearch MCP客户端
使用配置文件，提供简单易用的接口
"""

import asyncio
import json
from typing import Dict, Any, List, Optional
from fastmcp import Client
from fastmcp.client.transports import NpxStdioTransport
from elasticsearch_config import get_elasticsearch_config, validate_config, print_config

class SimpleElasticsearchMCPClient:
    """简化的Elasticsearch MCP客户端"""
    
    def __init__(self):
        """初始化客户端"""
        self.config = get_elasticsearch_config()
        self.client = None
        self.transport = None
        
    async def connect(self) -> bool:
        """连接到MCP服务器"""
        try:
            print("🔗 正在连接到Elasticsearch MCP服务器...")
            
            # 验证配置
            if not validate_config():
                return False
            
            # 创建传输
            self.transport = NpxStdioTransport(
                package="@elastic/mcp-server-elasticsearch",
                args=["-y"]
            )
            self.transport.env = self.config
            
            # 创建客户端
            self.client = Client(self.transport)
            
            # 连接
            await self.client.__aenter__()
            await self.client.ping()
            
            print("✅ 连接成功!")
            return True
            
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    async def disconnect(self):
        """断开连接"""
        if self.client:
            try:
                await self.client.__aexit__(None, None, None)
                print("🔌 已断开连接")
            except Exception as e:
                print(f"⚠️ 断开连接时出错: {e}")
    
    async def get_tools(self) -> List[Dict[str, Any]]:
        """获取可用工具"""
        if not self.client:
            raise Exception("未连接到服务器")
        
        tools = await self.client.list_tools()
        return [{"name": tool.name, "description": tool.description} for tool in tools]
    
    async def call_tool(self, tool_name: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """调用工具"""
        if not self.client:
            raise Exception("未连接到服务器")
        
        if params is None:
            params = {}
        
        try:
            result = await self.client.call_tool(tool_name, params)
            return {
                "success": True,
                "result": result[0].text if result else None
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def search(self, index: str, query_body: Dict[str, Any], size: int = 10) -> Dict[str, Any]:
        """搜索文档"""
        return await self.call_tool("search", {
            "index": index,
            "queryBody": query_body,
            "size": size
        })

    async def list_indices(self, index_pattern: str = "*") -> Dict[str, Any]:
        """列出索引"""
        return await self.call_tool("list_indices", {
            "indexPattern": index_pattern
        })

    async def get_mappings(self, index: str) -> Dict[str, Any]:
        """获取索引映射"""
        return await self.call_tool("get_mappings", {
            "index": index
        })

    async def get_shards(self, index: str = None) -> Dict[str, Any]:
        """获取分片信息"""
        params = {}
        if index:
            params["index"] = index
        return await self.call_tool("get_shards", params)

async def demo():
    """演示用法"""
    print("🚀 Elasticsearch MCP客户端演示")
    print("=" * 50)
    
    # 显示配置
    print_config()
    
    # 创建客户端
    client = SimpleElasticsearchMCPClient()
    
    try:
        # 连接
        if not await client.connect():
            return
        
        # 获取工具列表
        print("\n📋 获取可用工具...")
        tools = await client.get_tools()
        print(f"发现 {len(tools)} 个工具:")
        for i, tool in enumerate(tools, 1):
            print(f"  {i}. {tool['name']} - {tool['description']}")
        
        # 测试一些常用操作
        print("\n🧪 测试常用操作...")

        # 1. 列出索引
        print("\n1. 列出索引:")
        indices_result = await client.list_indices("*")
        if indices_result["success"]:
            print("✅ 索引列表获取成功")
            print(f"结果: {indices_result['result']}")
        else:
            print(f"❌ 获取失败: {indices_result['error']}")

        # 2. 获取分片信息
        print("\n2. 获取分片信息:")
        shards_result = await client.get_shards()
        if shards_result["success"]:
            print("✅ 分片信息获取成功")
            print(f"结果: {shards_result['result']}")
        else:
            print(f"❌ 获取失败: {shards_result['error']}")

        # 3. 简单搜索测试
        print("\n3. 简单搜索测试:")
        search_query = {"match_all": {}}
        search_result = await client.search("*", search_query, 1)
        if search_result["success"]:
            print("✅ 搜索成功")
            print(f"结果: {search_result['result']}")
        else:
            print(f"❌ 搜索失败: {search_result['error']}")
        
        print("\n🎉 演示完成!")
        
    except Exception as e:
        print(f"❌ 演示过程中出错: {e}")
    
    finally:
        await client.disconnect()

async def interactive_mode():
    """交互式模式"""
    print("🎯 Elasticsearch MCP交互式模式")
    print("=" * 50)
    
    client = SimpleElasticsearchMCPClient()
    
    try:
        if not await client.connect():
            return
        
        # 获取工具列表
        tools = await client.get_tools()
        
        print("\n可用命令:")
        print("  1. list-tools     - 列出所有工具")
        print("  2. search         - 搜索文档")
        print("  3. list-indices   - 列出索引")
        print("  4. get-mappings   - 获取索引映射")
        print("  5. get-shards     - 获取分片信息")
        print("  6. custom-call    - 自定义工具调用")
        print("  7. quit           - 退出")
        print("=" * 50)
        
        while True:
            try:
                command = input("\n请输入命令: ").strip().lower()
                
                if command in ['7', 'quit', 'exit', 'q']:
                    break
                elif command in ['1', 'list-tools']:
                    print(f"\n📋 可用工具 ({len(tools)}个):")
                    for i, tool in enumerate(tools, 1):
                        print(f"  {i}. {tool['name']} - {tool['description']}")

                elif command in ['2', 'search']:
                    index = input("索引名 (留空使用 '*'): ").strip() or "*"
                    print("搜索查询 (JSON格式，例如: {\"match_all\": {}} 或 {\"match\": {\"field\": \"value\"}})")
                    query_str = input("查询: ").strip()
                    size = input("结果数量 (默认10): ").strip()
                    size = int(size) if size.isdigit() else 10

                    try:
                        query = json.loads(query_str) if query_str else {"match_all": {}}
                        result = await client.search(index, query, size)
                        print(f"\n搜索结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
                    except json.JSONDecodeError:
                        print("❌ 查询格式错误，请使用有效的JSON格式")

                elif command in ['3', 'list-indices']:
                    pattern = input("索引模式 (默认 '*'): ").strip() or "*"
                    result = await client.list_indices(pattern)
                    print(f"\n索引列表: {json.dumps(result, indent=2, ensure_ascii=False)}")

                elif command in ['4', 'get-mappings']:
                    index = input("索引名: ").strip()
                    if index:
                        result = await client.get_mappings(index)
                        print(f"\n索引映射: {json.dumps(result, indent=2, ensure_ascii=False)}")
                    else:
                        print("❌ 请输入索引名")

                elif command in ['5', 'get-shards']:
                    index = input("索引名 (留空获取所有分片): ").strip()
                    result = await client.get_shards(index if index else None)
                    print(f"\n分片信息: {json.dumps(result, indent=2, ensure_ascii=False)}")

                elif command in ['6', 'custom-call']:
                    print("\n可用工具:")
                    for i, tool in enumerate(tools, 1):
                        print(f"  {i}. {tool['name']}")
                    
                    tool_name = input("工具名称: ").strip()
                    params_str = input("参数 (JSON格式，留空使用 {}): ").strip()
                    
                    try:
                        params = json.loads(params_str) if params_str else {}
                        result = await client.call_tool(tool_name, params)
                        print(f"\n调用结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
                    except json.JSONDecodeError:
                        print("❌ 参数格式错误")
                
                else:
                    print("❌ 未知命令")
                    
            except KeyboardInterrupt:
                print("\n\n👋 用户中断")
                break
            except Exception as e:
                print(f"❌ 执行命令时出错: {e}")
    
    finally:
        await client.disconnect()

def main():
    """主函数"""
    print("🚀 简化Elasticsearch MCP客户端")
    print("=" * 50)
    
    mode = input("选择模式:\n1. 演示模式\n2. 交互式模式\n请输入 (1/2): ").strip()
    
    if mode == "2":
        asyncio.run(interactive_mode())
    else:
        asyncio.run(demo())

if __name__ == "__main__":
    main()
