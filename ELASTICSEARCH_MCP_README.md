# Elasticsearch MCP Python客户端

这是一个用于连接Elasticsearch MCP服务器的Python客户端集合，使用FastMCP SDK构建。

## 📋 文件说明

### 核心文件

1. **`elasticsearch_config.py`** - 配置管理
   - 管理Elasticsearch连接配置
   - 支持环境变量覆盖
   - 提供配置验证功能

2. **`simple_es_mcp_client.py`** - 简化客户端 (推荐)
   - 易于使用的高级接口
   - 内置常用操作方法
   - 支持演示和交互式模式

3. **`elasticsearch_mcp_client.py`** - 完整客户端
   - 功能完整的客户端实现
   - 详细的错误处理
   - 高级自定义功能

4. **`test_elasticsearch_mcp.py`** - 测试脚本
   - 快速连接测试
   - 工具和资源发现
   - 调试信息输出

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装FastMCP
pip install fastmcp

# 确保已安装Elasticsearch MCP服务器
npm install -g @elastic/mcp-server-elasticsearch
```

### 2. 配置连接

你的配置已经在 `elasticsearch_config.py` 中设置好了：

```python
{
    "ES_URL": "https://medusa.ioq.com:19203",
    "ES_USERNAME": "elastic",
    "ES_PASSWORD": "e$search",
    "ES_VERSION": "6"
}
```

### 3. 运行测试

```bash
# 快速连接测试
python test_elasticsearch_mcp.py

# 简化客户端演示
python simple_es_mcp_client.py

# 完整客户端
python elasticsearch_mcp_client.py
```

## 🔧 使用示例

### 基本连接测试

```python
from simple_es_mcp_client import SimpleElasticsearchMCPClient

async def test_connection():
    client = SimpleElasticsearchMCPClient()
    
    if await client.connect():
        # 获取工具列表
        tools = await client.get_tools()
        print(f"可用工具: {len(tools)}个")
        
        # 搜索文档
        result = await client.search("my-index", "search query", 10)
        print(f"搜索结果: {result}")
        
        await client.disconnect()

# 运行
import asyncio
asyncio.run(test_connection())
```

### 交互式使用

```bash
python simple_es_mcp_client.py
# 选择模式: 2 (交互式模式)
```

可用命令：
- `list-tools` - 列出所有工具
- `search` - 搜索文档
- `cluster-health` - 获取集群健康状态
- `list-indices` - 列出索引
- `custom-call` - 自定义工具调用

## 📊 配置管理

### 使用环境变量

```bash
export ES_URL="https://your-elasticsearch-url:9200"
export ES_USERNAME="your-username"
export ES_PASSWORD="your-password"
export ES_VERSION="8"

python simple_es_mcp_client.py
```

### 验证配置

```python
from elasticsearch_config import validate_config, print_config

# 打印当前配置
print_config()

# 验证配置
if validate_config():
    print("配置有效")
```

## 🛠️ 可用工具

根据Elasticsearch MCP服务器，通常包含以下工具：

1. **搜索工具** - 在索引中搜索文档
2. **集群健康** - 获取集群状态
3. **索引管理** - 列出和管理索引
4. **文档操作** - 获取、创建、更新文档
5. **聚合查询** - 执行聚合分析

具体可用工具请运行测试脚本查看。

## 🔍 故障排除

### 常见问题

1. **连接失败**
   ```
   ❌ 连接失败: Server error
   ```
   - 检查Elasticsearch服务器是否可访问
   - 验证用户名和密码
   - 确认网络连接

2. **NPX包未找到**
   ```
   ❌ Package @elastic/mcp-server-elasticsearch not found
   ```
   - 安装MCP服务器: `npm install -g @elastic/mcp-server-elasticsearch`

3. **权限错误**
   ```
   ❌ Authentication failed
   ```
   - 检查ES_USERNAME和ES_PASSWORD
   - 确认用户有足够权限

### 调试模式

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 运行测试
python test_elasticsearch_mcp.py
```

## 📝 自定义使用

### 扩展客户端

```python
from simple_es_mcp_client import SimpleElasticsearchMCPClient

class MyElasticsearchClient(SimpleElasticsearchMCPClient):
    async def my_custom_search(self, index: str, query: dict):
        """自定义搜索方法"""
        return await self.call_tool("search", {
            "index": index,
            "body": query
        })
```

### 批量操作

```python
async def batch_operations():
    client = SimpleElasticsearchMCPClient()
    await client.connect()
    
    # 批量搜索
    indices = ["index1", "index2", "index3"]
    results = []
    
    for index in indices:
        result = await client.search(index, "*", 5)
        results.append(result)
    
    await client.disconnect()
    return results
```

## 🔗 相关链接

- [FastMCP文档](https://github.com/jlowin/fastmcp)
- [Elasticsearch MCP服务器](https://github.com/elastic/mcp-server-elasticsearch)
- [MCP协议规范](https://modelcontextprotocol.io/)

## 📄 许可证

本项目遵循MIT许可证。
