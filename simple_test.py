#!/usr/bin/env python3
"""
简单测试脚本 - 使用内存传输测试FastMCP服务器
"""

import asyncio
from fastmcp import Client

# 导入服务器模块
import streamable_http_mcp_server
import api_conversion_example

async def test_in_memory():
    """使用内存传输测试服务器"""
    print("🧪 Testing FastMCP Servers with In-Memory Transport")
    print("=" * 60)
    
    # 测试基础模板服务器
    print("\n🔧 Testing Basic Template Server (In-Memory)")
    print("-" * 50)
    
    try:
        async with Client(streamable_http_mcp_server.mcp) as client:
            # 测试连接
            await client.ping()
            print("✅ Server connection successful")
            
            # 列出工具
            tools = await client.list_tools()
            print(f"📋 Available tools: {len(tools)}")
            for tool in tools[:5]:  # 显示前5个工具
                print(f"  - {tool.name}")
            if len(tools) > 5:
                print(f"  ... and {len(tools) - 5} more")
            
            # 测试一些工具
            print("\n🛠️  Testing tools:")
            
            # 测试时间工具
            result = await client.call_tool("get_current_time")
            print(f"  ⏰ Current time: {result[0].text}")
            
            # 测试计算工具
            result = await client.call_tool("calculate_sum", {"numbers": [1, 2, 3, 4, 5]})
            print(f"  🔢 Sum calculation: {result[0].text}")
            
            # 测试文本格式化
            result = await client.call_tool("format_text", {"text": "hello world", "style": "title"})
            print(f"  📝 Text formatting: {result[0].text}")
            
            # 测试资源
            print("\n📚 Testing resources:")
            resources = await client.list_resources()
            print(f"📋 Available resources: {len(resources)}")
            for resource in resources[:3]:
                print(f"  - {resource.uri}")
            if len(resources) > 3:
                print(f"  ... and {len(resources) - 3} more")
            
            # 读取一个资源
            if resources:
                config = await client.read_resource("config://server")
                print(f"  ⚙️  Server config: Available")
            
            print("✅ Basic Template Server test completed successfully!")
            
    except Exception as e:
        print(f"❌ Basic Template Server test failed: {e}")
        return False
    
    # 测试API转换服务器
    print("\n🔧 Testing API Conversion Server (In-Memory)")
    print("-" * 50)
    
    try:
        async with Client(api_conversion_example.mcp) as client:
            # 测试连接
            await client.ping()
            print("✅ Server connection successful")
            
            # 列出工具
            tools = await client.list_tools()
            print(f"📋 Available tools: {len(tools)}")
            for tool in tools[:5]:  # 显示前5个工具
                print(f"  - {tool.name}")
            if len(tools) > 5:
                print(f"  ... and {len(tools) - 5} more")
            
            # 测试一些工具
            print("\n🛠️  Testing tools:")
            
            # 测试用户管理API
            result = await client.call_tool("get_user_info", {"user_id": 1})
            print(f"  👤 Get user info: Success")
            
            result = await client.call_tool("create_user", {
                "name": "测试用户",
                "email": "<EMAIL>"
            })
            print(f"  ➕ Create user: Success")
            
            # 测试产品管理API
            result = await client.call_tool("query_products", {"category": "电子产品"})
            print(f"  📦 Query products: Success")
            
            # 测试文件操作API
            result = await client.call_tool("list_files", {"directory": "."})
            print(f"  📁 List files: Success")
            
            # 测试资源
            print("\n📚 Testing resources:")
            resources = await client.list_resources()
            print(f"📋 Available resources: {len(resources)}")
            for resource in resources[:3]:
                print(f"  - {resource.uri}")
            if len(resources) > 3:
                print(f"  ... and {len(resources) - 3} more")
            
            print("✅ API Conversion Server test completed successfully!")
            
    except Exception as e:
        print(f"❌ API Conversion Server test failed: {e}")
        return False
    
    print("\n🎉 All tests passed! Your FastMCP servers are working correctly.")
    return True

def main():
    """主函数"""
    success = asyncio.run(test_in_memory())
    if success:
        print("\n✨ Summary: Both servers are functioning properly!")
        return 0
    else:
        print("\n⚠️  Some tests failed. Please check the error messages above.")
        return 1

if __name__ == "__main__":
    exit(main())
