# FastMCP Streamable HTTP Server Template

这是一个基于FastMCP的Streamable HTTP模式MCP服务器模板，帮助你快速将本地API接口转换为MCP服务器。

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install fastmcp httpx
```

### 2. 启动服务器

```bash
python streamable_http_mcp_server.py
```

服务器将在 `http://127.0.0.1:8000/mcp/` 启动

### 3. 测试服务器

```bash
# 推荐：运行内存传输测试（最可靠）
python test_servers.py

# 或者使用简单测试脚本
python simple_test.py

# 运行原始客户端示例
python mcp_client_example.py

# 运行交互式客户端
python mcp_client_example.py interactive
```

#### 测试选项

```bash
# 内存传输测试（推荐）
python test_servers.py memory

# HTTP传输测试（可能遇到问题）
python test_servers.py quick

# 测试单个服务器
python test_servers.py basic    # 基础模板服务器
python test_servers.py api      # API转换服务器

# 手动测试（假设服务器已运行）
python test_servers.py manual
```

## 📡 Streamable HTTP 模式特点

- **推荐用于新部署** - FastMCP官方推荐的传输方式
- **双向流支持** - 支持双向数据流传输
- **HTTP兼容** - 基于标准HTTP协议，易于部署和调试
- **高性能** - 优化的长连接处理
- **易于集成** - 可以轻松集成到现有的Web应用中

## 🛠️ 服务器功能

### 工具 (Tools)
- `get_current_time()` - 获取当前时间
- `calculate_sum(numbers)` - 计算数字总和
- `generate_random_data(count, min_val, max_val)` - 生成随机数据
- `format_text(text, style)` - 文本格式化
- `fetch_external_data(url)` - 获取外部数据
- `validate_email(email)` - 邮箱验证

### 资源 (Resources)
- `config://server` - 服务器配置信息
- `data://stats` - 服务器统计信息
- `help://tools/{tool_name}` - 工具帮助信息

### 提示模板 (Prompts)
- `api_integration_guide` - API集成指南
- `error_handling_guide` - 错误处理指南

## 🔧 自定义你的API

### 1. 添加工具函数

```python
@mcp.tool
def your_api_function(param1: str, param2: int) -> dict:
    """你的API函数描述"""
    # 在这里实现你的API逻辑
    return {"result": "success", "data": param1 * param2}
```

### 2. 添加资源

```python
@mcp.resource("your://resource/path")
def your_resource() -> dict:
    """你的资源描述"""
    return {"your": "data"}
```

### 3. 添加动态资源

```python
@mcp.resource("dynamic://data/{id}")
def dynamic_resource(id: str) -> dict:
    """动态资源，根据ID返回不同数据"""
    return {"id": id, "data": f"Data for {id}"}
```

## 🌐 客户端连接

### Python客户端

```python
from fastmcp import Client

async def connect_to_server():
    async with Client("http://127.0.0.1:8000/mcp/") as client:
        # 调用工具
        result = await client.call_tool("get_current_time")
        print(result[0].text)
        
        # 读取资源
        config = await client.read_resource("config://server")
        print(config[0].text)
```

### Claude Desktop配置

在Claude Desktop的配置文件中添加：

```json
{
  "mcpServers": {
    "your-server": {
      "url": "http://127.0.0.1:8000/mcp/",
      "transport": "http"
    }
  }
}
```

## 🔐 认证支持

FastMCP支持多种认证方式：

### Bearer Token认证

```python
from fastmcp.server.auth import BearerAuthProvider

auth = BearerAuthProvider(
    public_key=your_public_key,
    audience="your-server"
)

mcp = FastMCP(name="Secure Server", auth=auth)
```

### 自定义认证头

```python
from fastmcp import Client
from fastmcp.client.transports import StreamableHttpTransport

transport = StreamableHttpTransport(
    url="http://127.0.0.1:8000/mcp/",
    headers={"Authorization": "Bearer your-token"}
)

client = Client(transport)
```

## 🚀 部署选项

### 1. 本地开发

```bash
python streamable_http_mcp_server.py
```

### 2. 使用Uvicorn

```python
import uvicorn
from fastmcp import FastMCP

mcp = FastMCP("Production Server")
app = mcp.http_app()

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

### 3. 集成到FastAPI

```python
from fastapi import FastAPI
from fastmcp import FastMCP

mcp = FastMCP("API Server")
mcp_app = mcp.http_app()

app = FastAPI(lifespan=mcp_app.lifespan)
app.mount("/mcp", mcp_app)
```

### 4. 使用ngrok公开访问

```bash
# 启动服务器
python streamable_http_mcp_server.py

# 在另一个终端
ngrok http 8000
```

## 📊 监控和调试

### 使用MCP Inspector

```bash
# 安装并启动Inspector
fastmcp dev streamable_http_mcp_server.py
```

### 日志配置

```python
mcp.run(
    transport="http",
    log_level="debug"  # debug, info, warning, error
)
```

## 🔄 从现有API迁移

### 1. REST API转换

```python
# 原始REST API
@app.get("/api/users/{user_id}")
def get_user(user_id: int):
    return {"id": user_id, "name": "User"}

# 转换为MCP工具
@mcp.tool
def get_user(user_id: int) -> dict:
    """获取用户信息"""
    return {"id": user_id, "name": "User"}
```

### 2. 数据库操作

```python
@mcp.tool
async def query_database(sql: str) -> list:
    """执行数据库查询"""
    # 你的数据库逻辑
    async with database.connection() as conn:
        result = await conn.fetch(sql)
        return [dict(row) for row in result]
```

### 3. 文件操作

```python
@mcp.tool
def read_file(filepath: str) -> str:
    """读取文件内容"""
    with open(filepath, 'r', encoding='utf-8') as f:
        return f.read()
```

## 🤝 最佳实践

1. **错误处理** - 始终包含适当的错误处理
2. **类型注解** - 使用Python类型注解提高可读性
3. **文档字符串** - 为所有工具和资源添加描述
4. **参数验证** - 验证输入参数的有效性
5. **异步支持** - 对于I/O操作使用异步函数
6. **资源管理** - 正确管理数据库连接等资源

## 📚 更多资源

- [FastMCP官方文档](https://github.com/jlowin/fastmcp)
- [MCP协议规范](https://modelcontextprotocol.io/)
- [示例项目](https://github.com/jlowin/fastmcp/tree/main/examples)

## 🐛 故障排除

### 已知问题

1. **HTTP传输502错误** - 当前FastMCP的HTTP传输可能存在问题，建议使用内存传输进行测试
2. **端口占用** - 如果遇到端口占用错误，请检查是否有其他进程在使用8000或8001端口

### 解决方案

1. **推荐使用内存传输测试**：
   ```bash
   python test_servers.py memory
   python simple_test.py
   ```

2. **检查端口占用**：
   ```bash
   lsof -ti:8000,8001  # 查看占用端口的进程
   kill -9 <PID>       # 杀死占用端口的进程
   ```

3. **FastMCP构造函数修复**：
   - 已修复：将`description`参数改为`instructions`
   - 如果遇到类似错误，请检查FastMCP构造函数参数

### 常见问题

1. **连接失败** - 检查服务器是否正在运行，端口是否正确
2. **工具调用失败** - 检查参数类型和必需参数
3. **资源访问失败** - 确认资源URI格式正确
4. **认证失败** - 检查认证配置和令牌

### 调试技巧

```python
# 启用详细日志
mcp.run(transport="http", log_level="debug")

# 使用try-catch捕获错误
@mcp.tool
def safe_operation(data: str) -> dict:
    try:
        # 你的操作
        return {"success": True}
    except Exception as e:
        return {"error": str(e), "success": False}
```

### 测试状态

- ✅ **内存传输**: 完全正常工作
- ✅ **服务器功能**: 所有工具和资源正常
- ⚠️ **HTTP传输**: 存在502 Bad Gateway问题
- ✅ **构造函数**: 已修复`description`参数问题
