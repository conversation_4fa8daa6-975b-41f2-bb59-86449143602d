# Elasticsearch Agent - 智能数据查询助手

一个基于FastMCP和DeepSeek-R1的智能Elasticsearch数据查询和分析助手，提供Web界面进行交互。

## 🌟 特性

- 🤖 **智能对话** - 使用DeepSeek-R1模型进行自然语言交互
- 🔍 **数据查询** - 智能构建和执行Elasticsearch查询
- 📊 **数据分析** - 提供数据统计、趋势分析和异常检测
- 🛠️ **索引管理** - 查看索引、映射和分片信息
- 🌐 **Web界面** - 现代化的聊天界面，支持实时对话
- ⚡ **实时通信** - WebSocket支持流式响应

## 📁 项目结构

```
elasticsearch-agent/
├── README.md              # 项目说明
├── requirements.txt       # Python依赖
├── start.py              # 启动脚本
├── config/               # 配置文件
│   ├── mcp_config.json   # MCP服务器配置
│   └── agent_config.py   # Agent配置
├── backend/              # 后端服务
│   ├── elasticsearch_agent.py  # 核心Agent类
│   └── api_server.py     # FastAPI服务器
└── frontend/             # 前端界面
    └── index.html        # Web界面
```

## 🚀 快速开始

### 1. 安装依赖

```bash
# Python依赖
pip install -r requirements.txt

# Elasticsearch MCP服务器
npm install -g @elastic/mcp-server-elasticsearch
```

### 2. 配置检查

确认配置文件中的Elasticsearch连接信息：

```json
{
  "ES_URL": "https://medusa.iotxmm.com:19203",
  "ES_USERNAME": "elastic",
  "ES_PASSWORD": "elastic$2020$search",
  "ES_VERSION": "6"
}
```

### 3. 启动服务

```bash
# 使用启动脚本（推荐）
python start.py

# 或手动启动
cd backend && python api_server.py
cd frontend && python -m http.server 3000
```

### 4. 访问界面

- 🌐 **Web界面**: http://localhost:3000
- 📊 **API文档**: http://localhost:8080/docs
- 🔧 **API端点**: http://localhost:8080

## 🎯 功能说明

### 智能对话功能

- **自然语言查询** - 用自然语言描述查询需求
- **智能建议** - 提供查询优化和最佳实践建议
- **结果解释** - 详细解释查询结果和数据含义
- **错误诊断** - 分析和解决查询问题

### 数据操作功能

1. **索引管理**
   - 列出所有索引
   - 查看索引映射
   - 分析分片分布

2. **数据搜索**
   - 全文搜索
   - 结构化查询
   - 聚合分析
   - 时间范围查询

3. **数据分析**
   - 统计分析
   - 趋势识别
   - 异常检测
   - 性能分析

### 快速操作

界面提供常用操作的快捷按钮：
- 📋 列出索引
- 🔍 搜索最近数据
- ⚠️ 分析错误日志
- 📊 集群健康检查

## 🛠️ API接口

### 核心接口

- `POST /chat` - 对话接口
- `GET /tools` - 获取可用工具
- `POST /tool/execute` - 执行工具
- `GET /indices` - 获取索引列表
- `POST /search` - 搜索数据

### WebSocket

- `ws://localhost:8080/ws` - 实时对话

## 🔧 配置说明

### LLM配置

```python
llm_config = {
    "base_url": "http://ai.ai.iot.chinamobile.com/imaas/v1",
    "api_key": "sk-y3dYMll9oWtQNgj2I9dKO8UAHS24tcMgr6alaDl0EWfqU5Db",
    "model": "DeepSeek-R1",
    "temperature": 0.7,
    "max_tokens": 4000
}
```

### MCP配置

```json
{
  "mcpServers": {
    "elasticsearch-mcp-server": {
      "command": "npx",
      "args": ["-y", "@elastic/mcp-server-elasticsearch"],
      "env": {
        "ES_URL": "https://medusa.iotxmm.com:19203",
        "ES_USERNAME": "elastic",
        "ES_PASSWORD": "elastic$2020$search",
        "ES_VERSION": "6"
      }
    }
  }
}
```

## 💡 使用示例

### 基本查询

```
用户: "请列出所有的索引"
Agent: 正在为您查询所有索引...
[显示索引列表和详细信息]
```

### 数据搜索

```
用户: "搜索最近1小时的错误日志"
Agent: 我来帮您搜索最近1小时的错误日志...
[构建查询并显示结果]
```

### 数据分析

```
用户: "分析用户访问模式"
Agent: 正在分析用户访问数据...
[提供统计分析和可视化建议]
```

## 🔍 故障排除

### 常见问题

1. **连接失败**
   - 检查Elasticsearch服务器地址
   - 验证用户名和密码
   - 确认网络连接

2. **MCP服务器错误**
   - 确保已安装 `@elastic/mcp-server-elasticsearch`
   - 检查Node.js版本
   - 验证环境变量

3. **LLM调用失败**
   - 检查API密钥
   - 验证模型名称
   - 确认网络连接

### 调试模式

```bash
# 启用详细日志
export LOG_LEVEL=DEBUG
python backend/api_server.py
```

## 📝 开发说明

### 扩展Agent功能

```python
# 在elasticsearch_agent.py中添加新方法
async def custom_analysis(self, data_type: str):
    """自定义分析功能"""
    # 实现自定义逻辑
    pass
```

### 添加新的API端点

```python
# 在api_server.py中添加新路由
@app.post("/custom/endpoint")
async def custom_endpoint(request: CustomRequest):
    # 实现自定义端点
    pass
```

## 🔗 相关链接

- [FastMCP文档](https://github.com/jlowin/fastmcp)
- [Elasticsearch MCP服务器](https://github.com/elastic/mcp-server-elasticsearch)
- [DeepSeek模型](https://www.deepseek.com/)
- [FastAPI文档](https://fastapi.tiangolo.com/)

## 📄 许可证

本项目遵循MIT许可证。

---

## 🎉 开始使用

1. 安装依赖: `pip install -r requirements.txt`
2. 安装MCP服务器: `npm install -g @elastic/mcp-server-elasticsearch`
3. 启动服务: `python start.py`
4. 访问界面: http://localhost:3000

享受智能数据查询体验！🚀
