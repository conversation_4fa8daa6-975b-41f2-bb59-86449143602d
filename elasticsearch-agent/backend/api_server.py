#!/usr/bin/env python3
"""
FastAPI后端服务器
"""

import asyncio
import json
import logging
import sys
import os
from typing import Dict, Any, List
from contextlib import asynccontextmanager

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse, HTMLResponse
from pydantic import BaseModel

from elasticsearch_agent import agent
from config.agent_config import config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 请求模型
class ChatRequest(BaseModel):
    message: str
    stream: bool = False

class ToolRequest(BaseModel):
    tool_name: str
    parameters: Dict[str, Any] = {}

class SearchRequest(BaseModel):
    index: str
    query: Dict[str, Any]
    size: int = 10

# 应用生命周期管理
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时初始化Agent
    logger.info("🚀 启动Elasticsearch Agent服务...")
    success = await agent.initialize()
    if not success:
        logger.error("❌ Agent初始化失败!")
        raise Exception("Agent初始化失败")
    
    logger.info("✅ Agent服务启动成功!")
    yield
    
    # 关闭时清理资源
    logger.info("🔌 关闭Agent服务...")
    await agent.close()

# 创建FastAPI应用
app = FastAPI(
    title="Elasticsearch Agent API",
    description="智能Elasticsearch数据查询和分析助手",
    version="1.0.0",
    lifespan=lifespan
)

# 配置CORS
server_config = config.get_server_config()
app.add_middleware(
    CORSMiddleware,
    allow_origins=server_config["cors_origins"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# WebSocket连接管理
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

manager = ConnectionManager()

# 前端HTML内容
def get_frontend_html():
    """获取前端HTML"""
    try:
        frontend_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "frontend", "index.html")
        with open(frontend_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        logger.error(f"读取前端文件失败: {e}")
        return "<h1>前端文件加载失败</h1>"

# API路由
@app.get("/")
async def root():
    """根路径 - 返回前端页面"""
    return HTMLResponse(content=get_frontend_html())

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "agent_initialized": agent.mcp_client is not None,
        "tools_count": len(agent.available_tools)
    }

@app.get("/tools")
async def get_tools():
    """获取可用工具"""
    return {
        "tools": agent.available_tools,
        "count": len(agent.available_tools)
    }

@app.post("/chat")
async def chat(request: ChatRequest):
    """对话接口"""
    try:
        if request.stream:
            # 流式响应
            async def generate():
                async for chunk in agent.chat(request.message, stream=True):
                    yield f"data: {json.dumps({'content': chunk})}\n\n"
                yield f"data: {json.dumps({'done': True})}\n\n"
            
            return StreamingResponse(
                generate(),
                media_type="text/plain",
                headers={"Cache-Control": "no-cache"}
            )
        else:
            # 普通响应
            response = ""
            async for chunk in agent.chat(request.message, stream=False):
                response += chunk
            
            return {
                "response": response,
                "timestamp": agent.conversation_history[-1]["timestamp"] if agent.conversation_history else None
            }
    
    except Exception as e:
        logger.error(f"❌ 对话处理失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/tool/execute")
async def execute_tool(request: ToolRequest):
    """执行工具"""
    try:
        result = await agent.execute_tool(request.tool_name, request.parameters)
        return result
    except Exception as e:
        logger.error(f"❌ 工具执行失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/indices")
async def get_indices():
    """获取索引列表"""
    try:
        result = await agent.get_indices()
        return result
    except Exception as e:
        logger.error(f"❌ 获取索引失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/search")
async def search_data(request: SearchRequest):
    """搜索数据"""
    try:
        result = await agent.search_data(request.index, request.query, request.size)
        return result
    except Exception as e:
        logger.error(f"❌ 搜索失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/mappings/{index}")
async def get_mappings(index: str):
    """获取索引映射"""
    try:
        result = await agent.get_mappings(index)
        return result
    except Exception as e:
        logger.error(f"❌ 获取映射失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/shards")
async def get_shards(index: str = None):
    """获取分片信息"""
    try:
        result = await agent.get_shards(index)
        return result
    except Exception as e:
        logger.error(f"❌ 获取分片信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/conversation/history")
async def get_conversation_history():
    """获取对话历史"""
    return {
        "history": agent.get_conversation_history(),
        "count": len(agent.conversation_history)
    }

@app.delete("/conversation/history")
async def clear_conversation_history():
    """清空对话历史"""
    agent.clear_conversation_history()
    return {"message": "对话历史已清空"}

# WebSocket端点
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket对话端点"""
    await manager.connect(websocket)
    try:
        while True:
            # 接收消息
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            user_message = message_data.get("message", "")
            if not user_message:
                continue
            
            # 发送用户消息确认
            await manager.send_personal_message(
                json.dumps({
                    "type": "user_message",
                    "content": user_message
                }),
                websocket
            )
            
            # 流式发送Agent回复
            await manager.send_personal_message(
                json.dumps({
                    "type": "assistant_start"
                }),
                websocket
            )
            
            async for chunk in agent.chat(user_message, stream=True):
                await manager.send_personal_message(
                    json.dumps({
                        "type": "assistant_chunk",
                        "content": chunk
                    }),
                    websocket
                )
            
            await manager.send_personal_message(
                json.dumps({
                    "type": "assistant_end"
                }),
                websocket
            )
            
    except WebSocketDisconnect:
        manager.disconnect(websocket)
        logger.info("WebSocket连接断开")
    except Exception as e:
        logger.error(f"WebSocket错误: {e}")
        manager.disconnect(websocket)

if __name__ == "__main__":
    import uvicorn
    
    server_config = config.get_server_config()
    
    print("🚀 启动Elasticsearch Agent API服务器")
    print("=" * 50)
    print(f"地址: http://{server_config['host']}:{server_config['port']}")
    print(f"文档: http://{server_config['host']}:{server_config['port']}/docs")
    print("=" * 50)
    
    uvicorn.run(
        "api_server:app",
        host=server_config["host"],
        port=server_config["port"],
        reload=server_config["debug"],
        log_level="info"
    )
