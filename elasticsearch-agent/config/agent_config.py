#!/usr/bin/env python3
"""
Agent配置文件
"""

import os
from typing import Dict, Any

class AgentConfig:
    """Agent配置类"""
    
    def __init__(self):
        # LLM配置
        self.llm_config = {
            "base_url": "http://ai.ai.iot.chinamobile.com/imaas/v1",
            "api_key": "sk-y3dYMll9oWtQNgj2I9dKO8UAHS24tcMgr6alaDl0EWfqU5Db",
            "model": "DeepSeek-R1",  # 根据API示例更新模型名称
            "temperature": 0.7,
            "max_tokens": 4000,
            "stream": True  # 支持流式输出
        }
        
        # MCP配置
        self.mcp_config = {
            "mcpServers": {
                "elasticsearch-mcp-server": {
                    "command": "npx",
                    "args": [
                        "-y",
                        "@elastic/mcp-server-elasticsearch"
                    ],
                    "env": {
                        "ES_URL": "https://medusa.iotxmm.com:19203",
                        "ES_USERNAME": "elastic",
                        "ES_PASSWORD": "elastic$2020$search",
                        "ES_VERSION": "6"
                    },
                    "timeout": 600
                }
            }
        }
        
        # Agent配置
        self.agent_config = {
            "name": "Elasticsearch Assistant",
            "description": "专业的Elasticsearch数据查询和分析助手",
            "system_prompt": self._get_system_prompt(),
            "max_conversation_history": 20,
            "enable_streaming": True
        }
        
        # 服务器配置
        self.server_config = {
            "host": "0.0.0.0",
            "port": 8080,
            "debug": True,
            "cors_origins": ["http://localhost:3000", "http://127.0.0.1:3000"]
        }
    
    def _get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """你是一个专业的Elasticsearch数据查询和分析助手。你可以帮助用户：

1. **索引管理**：
   - 列出和查看Elasticsearch索引
   - 获取索引映射和结构信息
   - 分析索引的分片和存储情况

2. **数据搜索**：
   - 执行各种类型的Elasticsearch查询
   - 构建复杂的查询DSL
   - 提供搜索结果的解释和分析

3. **数据分析**：
   - 分析日志数据模式
   - 提供数据统计和聚合分析
   - 识别异常和趋势

4. **查询优化**：
   - 建议更高效的查询方式
   - 解释查询性能
   - 提供最佳实践建议

**使用指南**：
- 你可以直接询问关于Elasticsearch的任何问题
- 我会使用可用的工具来查询和分析数据
- 我会以清晰、结构化的方式展示结果
- 如果需要，我会提供查询的详细解释

**可用工具**：
- list_indices: 列出所有索引
- get_mappings: 获取索引映射
- search: 执行搜索查询
- get_shards: 获取分片信息

请告诉我你想要查询或分析什么数据！"""

    def get_llm_config(self) -> Dict[str, Any]:
        """获取LLM配置"""
        return self.llm_config.copy()
    
    def get_mcp_config(self) -> Dict[str, Any]:
        """获取MCP配置"""
        return self.mcp_config.copy()
    
    def get_agent_config(self) -> Dict[str, Any]:
        """获取Agent配置"""
        return self.agent_config.copy()
    
    def get_server_config(self) -> Dict[str, Any]:
        """获取服务器配置"""
        return self.server_config.copy()

# 全局配置实例
config = AgentConfig()

if __name__ == "__main__":
    # 测试配置
    print("🧪 Agent配置测试")
    print("=" * 50)
    
    print("LLM配置:")
    llm_config = config.get_llm_config()
    for key, value in llm_config.items():
        if "api_key" in key:
            print(f"  {key}: {value[:10]}...")
        else:
            print(f"  {key}: {value}")
    
    print("\nMCP配置:")
    mcp_config = config.get_mcp_config()
    print(f"  服务器数量: {len(mcp_config['mcpServers'])}")
    for name in mcp_config['mcpServers'].keys():
        print(f"  - {name}")
    
    print("\nAgent配置:")
    agent_config = config.get_agent_config()
    print(f"  名称: {agent_config['name']}")
    print(f"  描述: {agent_config['description']}")
    
    print("\n✅ 配置加载成功!")
