#!/usr/bin/env python3
"""
测试LLM API调用
"""

import asyncio
import json
from openai import AsyncOpenAI

# 配置
CONFIG = {
    "base_url": "http://ai.ai.iot.chinamobile.com/imaas/v1",
    "api_key": "sk-y3dYMll9oWtQNgj2I9dKO8UAHS24tcMgr6alaDl0EWfqU5Db",
    "model": "Qwen2-0.5B-Instruct"
}

async def test_llm_api():
    """测试LLM API"""
    print("🧪 测试LLM API调用")
    print("=" * 50)
    
    try:
        # 创建客户端
        client = AsyncOpenAI(
            base_url=CONFIG["base_url"],
            api_key=CONFIG["api_key"]
        )
        
        print(f"📡 API地址: {CONFIG['base_url']}")
        print(f"🤖 模型: {CONFIG['model']}")
        print(f"🔑 API Key: {CONFIG['api_key'][:10]}...")
        
        # 测试消息
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "你好，请简单介绍一下你自己"}
        ]
        
        print("\n💬 发送测试消息...")
        print("用户: 你好，请简单介绍一下你自己")
        
        # 非流式调用测试
        print("\n🔄 测试非流式调用...")
        response = await client.chat.completions.create(
            model=CONFIG["model"],
            messages=messages,
            temperature=0.7,
            max_tokens=500
        )
        
        print("✅ 非流式调用成功!")
        print(f"回复: {response.choices[0].message.content}")
        
        # 流式调用测试
        print("\n🌊 测试流式调用...")
        print("回复: ", end="", flush=True)
        
        stream_response = await client.chat.completions.create(
            model=CONFIG["model"],
            messages=messages,
            temperature=0.7,
            max_tokens=500,
            stream=True
        )
        
        full_response = ""
        async for chunk in stream_response:
            if chunk.choices[0].delta.content:
                content = chunk.choices[0].delta.content
                print(content, end="", flush=True)
                full_response += content
        
        print("\n✅ 流式调用成功!")
        print(f"完整回复长度: {len(full_response)} 字符")
        
        print("\n🎉 所有测试通过!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        
        # 详细错误信息
        if hasattr(e, 'response'):
            print(f"HTTP状态码: {e.response.status_code}")
            print(f"响应内容: {e.response.text}")

async def test_curl_equivalent():
    """测试等效的curl调用"""
    print("\n🔧 等效的curl命令:")
    print("=" * 50)
    
    curl_command = f"""curl -i -X POST \\
   -H "Content-Type:application/json" \\
   -H "Authorization:Bearer {CONFIG['api_key']}" \\
   -d '{{
     "model": "{CONFIG['model']}", 
     "stream": true, 
     "messages": [
       {{"role": "system", "content": "You are a helpful assistant."}}, 
       {{"role": "user", "content": "你好，请简单介绍一下你自己"}}
     ]
   }}' \\
   '{CONFIG['base_url']}/chat/completions'"""
   
    print(curl_command)

if __name__ == "__main__":
    asyncio.run(test_llm_api())
    asyncio.run(test_curl_equivalent())
