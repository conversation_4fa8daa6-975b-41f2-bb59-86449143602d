#!/bin/bash

# FastMCP Streamable HTTP Server 启动脚本

echo "🚀 FastMCP Streamable HTTP Server Launcher"
echo "=========================================="

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 is not installed. Please install Python3 first."
    exit 1
fi

# 检查pip是否安装
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is not installed. Please install pip3 first."
    exit 1
fi

# 安装依赖
echo "📦 Installing dependencies..."
pip3 install -r requirements.txt

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies."
    exit 1
fi

echo "✅ Dependencies installed successfully."

# 选择要启动的服务器
echo ""
echo "请选择要启动的服务器:"
echo "1. 基础模板服务器 (streamable_http_mcp_server.py)"
echo "2. API转换示例服务器 (api_conversion_example.py)"
echo "3. 同时启动两个服务器"

read -p "请输入选择 (1-3): " choice

case $choice in
    1)
        echo "🌟 Starting Basic Template Server..."
        python3 streamable_http_mcp_server.py
        ;;
    2)
        echo "🔄 Starting API Conversion Example Server..."
        python3 api_conversion_example.py
        ;;
    3)
        echo "🚀 Starting both servers..."
        echo "📡 Basic Template Server will run on port 8000"
        echo "🔄 API Conversion Server will run on port 8001"
        
        # 在后台启动第一个服务器
        python3 streamable_http_mcp_server.py &
        SERVER1_PID=$!
        
        # 等待一秒
        sleep 1
        
        # 启动第二个服务器
        python3 api_conversion_example.py &
        SERVER2_PID=$!
        
        echo "✅ Both servers started!"
        echo "📡 Basic Template: http://127.0.0.1:8000/mcp/"
        echo "🔄 API Conversion: http://127.0.0.1:8001/mcp/"
        echo ""
        echo "Press Ctrl+C to stop both servers..."
        
        # 等待用户中断
        trap "echo '🛑 Stopping servers...'; kill $SERVER1_PID $SERVER2_PID; exit" INT
        wait
        ;;
    *)
        echo "❌ Invalid choice. Please run the script again."
        exit 1
        ;;
esac
