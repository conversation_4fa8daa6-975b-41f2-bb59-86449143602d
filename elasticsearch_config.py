#!/usr/bin/env python3
"""
Elasticsearch MCP配置文件
"""

import os
from typing import Dict, Any

class ElasticsearchConfig:
    """Elasticsearch MCP配置类"""
    
    def __init__(self):
        """初始化配置"""
        # 默认配置 (与你的cursor配置一致)
        self.default_config = {
            "ES_URL": "https://medusa.iotxmm.com:19203",
            "ES_USERNAME": "elastic",
            "ES_PASSWORD": "elastic@remotelog2020", 
            "ES_VERSION": "6"
        }
        
        # 从环境变量覆盖配置
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, str]:
        """加载配置，优先使用环境变量"""
        config = self.default_config.copy()
        
        # 从环境变量读取
        for key in config.keys():
            env_value = os.getenv(key)
            if env_value:
                config[key] = env_value
        
        return config
    
    def get_config(self) -> Dict[str, str]:
        """获取配置"""
        return self.config.copy()
    
    def get_mcp_config(self) -> Dict[str, Any]:
        """获取MCP服务器配置格式"""
        return {
            "mcpServers": {
                "elasticsearch-mcp-server": {
                    "command": "npx",
                    "args": [
                        "-y",
                        "@elastic/mcp-server-elasticsearch"
                    ],
                    "env": self.config,
                    "timeout": 600
                }
            }
        }
    
    def print_config(self, hide_password: bool = True):
        """打印配置信息"""
        print("📋 Elasticsearch MCP配置:")
        print("-" * 40)
        
        for key, value in self.config.items():
            if hide_password and "PASSWORD" in key:
                print(f"  {key}: {'*' * len(value)}")
            else:
                print(f"  {key}: {value}")
        print()
    
    def validate_config(self) -> bool:
        """验证配置"""
        required_keys = ["ES_URL", "ES_USERNAME", "ES_PASSWORD"]
        
        for key in required_keys:
            if not self.config.get(key):
                print(f"❌ 缺少必需的配置: {key}")
                return False
        
        # 验证URL格式
        url = self.config["ES_URL"]
        if not (url.startswith("http://") or url.startswith("https://")):
            print(f"❌ 无效的ES_URL格式: {url}")
            return False
        
        print("✅ 配置验证通过")
        return True

# 全局配置实例
es_config = ElasticsearchConfig()

# 便捷函数
def get_elasticsearch_config() -> Dict[str, str]:
    """获取Elasticsearch配置"""
    return es_config.get_config()

def get_mcp_config() -> Dict[str, Any]:
    """获取MCP配置"""
    return es_config.get_mcp_config()

def print_config():
    """打印配置"""
    es_config.print_config()

def validate_config() -> bool:
    """验证配置"""
    return es_config.validate_config()

if __name__ == "__main__":
    # 测试配置
    print("🧪 测试Elasticsearch MCP配置")
    print("=" * 50)
    
    print_config()
    
    if validate_config():
        print("\n📄 MCP配置格式:")
        import json
        mcp_config = get_mcp_config()
        print(json.dumps(mcp_config, indent=2, ensure_ascii=False))
    else:
        print("\n❌ 配置验证失败")
