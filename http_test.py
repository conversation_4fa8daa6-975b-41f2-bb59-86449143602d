#!/usr/bin/env python3
"""
HTTP传输测试脚本 - 测试FastMCP HTTP服务器
"""

import asyncio
import subprocess
import time
import httpx
from fastmcp import Client

async def test_http_server():
    """测试HTTP服务器"""
    print("🧪 Testing FastMCP HTTP Server")
    print("=" * 40)
    
    # 启动服务器
    print("🚀 Starting HTTP server...")
    process = subprocess.Popen(
        ["python", "streamable_http_mcp_server.py"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    try:
        # 等待服务器启动
        print("⏳ Waiting for server to start...")
        await asyncio.sleep(3)
        
        # 检查进程是否还在运行
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            print(f"❌ Server failed to start:")
            print(f"stdout: {stdout}")
            print(f"stderr: {stderr}")
            return False
        
        print("✅ Server process is running")
        
        # 尝试不同的连接方法
        server_url = "http://127.0.0.1:8000/mcp/"
        
        # 方法1: 直接HTTP请求测试
        print(f"\n🔍 Testing HTTP endpoint: {server_url}")
        try:
            async with httpx.AsyncClient() as http_client:
                response = await http_client.get(server_url, timeout=5.0)
                print(f"HTTP Response: {response.status_code}")
                if response.headers:
                    print("Response headers:")
                    for key, value in response.headers.items():
                        print(f"  {key}: {value}")
        except Exception as e:
            print(f"HTTP request failed: {e}")
        
        # 方法2: 尝试MCP客户端连接
        print(f"\n🔗 Testing MCP client connection...")
        try:
            async with Client(server_url) as client:
                await client.ping()
                print("✅ MCP client connection successful")
                
                # 测试工具列表
                tools = await client.list_tools()
                print(f"📋 Available tools: {len(tools)}")
                
                # 测试一个工具
                result = await client.call_tool("get_current_time")
                print(f"⏰ Current time: {result[0].text}")
                
                return True
                
        except Exception as e:
            print(f"❌ MCP client connection failed: {e}")
            
            # 尝试其他可能的端点
            alternative_urls = [
                "http://127.0.0.1:8000/",
                "http://127.0.0.1:8000/mcp",
                "http://127.0.0.1:8000/sse/",
            ]
            
            for alt_url in alternative_urls:
                print(f"\n🔄 Trying alternative URL: {alt_url}")
                try:
                    async with Client(alt_url) as client:
                        await client.ping()
                        print(f"✅ Success with {alt_url}")
                        return True
                except Exception as alt_e:
                    print(f"❌ Failed with {alt_url}: {alt_e}")
            
            return False
    
    finally:
        # 清理进程
        print("\n🛑 Stopping server...")
        process.terminate()
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()
            process.wait()

async def test_with_explicit_transport():
    """使用显式传输测试"""
    print("\n🧪 Testing with Explicit StreamableHttpTransport")
    print("=" * 50)
    
    # 启动服务器
    print("🚀 Starting HTTP server...")
    process = subprocess.Popen(
        ["python", "streamable_http_mcp_server.py"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    try:
        # 等待服务器启动
        await asyncio.sleep(3)
        
        # 检查进程是否还在运行
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            print(f"❌ Server failed to start:")
            print(f"stdout: {stdout}")
            print(f"stderr: {stderr}")
            return False
        
        # 使用显式传输
        from fastmcp.client.transports import StreamableHttpTransport
        
        transport = StreamableHttpTransport(url="http://127.0.0.1:8000/mcp/")
        client = Client(transport)
        
        try:
            async with client:
                await client.ping()
                print("✅ Explicit transport connection successful")
                
                tools = await client.list_tools()
                print(f"📋 Available tools: {len(tools)}")
                
                return True
                
        except Exception as e:
            print(f"❌ Explicit transport failed: {e}")
            return False
    
    finally:
        # 清理进程
        print("\n🛑 Stopping server...")
        process.terminate()
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()
            process.wait()

def main():
    """主函数"""
    async def run_tests():
        success1 = await test_http_server()
        success2 = await test_with_explicit_transport()
        return success1 or success2
    
    success = asyncio.run(run_tests())
    
    if success:
        print("\n🎉 HTTP transport test successful!")
        return 0
    else:
        print("\n⚠️  HTTP transport tests failed.")
        print("💡 The servers work correctly with in-memory transport.")
        print("💡 HTTP transport may need additional configuration or debugging.")
        return 1

if __name__ == "__main__":
    exit(main())
