#!/usr/bin/env python3
"""
FastMCP Streamable HTTP Client Example
展示如何连接和使用Streamable HTTP MCP服务器
"""

import asyncio
from fastmcp import Client

async def test_mcp_server():
    """测试MCP服务器的各种功能"""
    
    # 连接到Streamable HTTP MCP服务器
    server_url = "http://127.0.0.1:8000/mcp/"
    
    print("🔗 Connecting to MCP Server:", server_url)
    
    async with Client(server_url) as client:
        try:
            # 1. 测试服务器连接
            print("\n📡 Testing server connection...")
            await client.ping()
            print("✅ Server connection successful!")
            
            # 2. 列出可用工具
            print("\n🛠️  Available tools:")
            tools = await client.list_tools()
            for tool in tools:
                print(f"  - {tool.name}: {tool.description}")
            
            # 3. 列出可用资源
            print("\n📚 Available resources:")
            resources = await client.list_resources()
            for resource in resources:
                print(f"  - {resource.uri}: {resource.name}")
            
            # 4. 列出可用提示
            print("\n💡 Available prompts:")
            prompts = await client.list_prompts()
            for prompt in prompts:
                print(f"  - {prompt.name}: {prompt.description}")
            
            # 5. 测试各种工具
            print("\n🧪 Testing tools...")
            
            # 测试时间工具
            print("\n⏰ Getting current time:")
            time_result = await client.call_tool("get_current_time")
            print(f"Current time: {time_result[0].text}")
            
            # 测试计算工具
            print("\n🔢 Testing calculation:")
            calc_result = await client.call_tool("calculate_sum", {"numbers": [1, 2, 3, 4, 5]})
            print(f"Sum of [1,2,3,4,5]: {calc_result[0].text}")
            
            # 测试随机数据生成
            print("\n🎲 Generating random data:")
            random_result = await client.call_tool("generate_random_data", {
                "count": 3,
                "min_val": 10,
                "max_val": 50
            })
            print(f"Random numbers: {random_result[0].text}")
            
            # 测试文本格式化
            print("\n📝 Testing text formatting:")
            format_result = await client.call_tool("format_text", {
                "text": "Hello MCP World",
                "style": "upper"
            })
            print(f"Formatted text: {format_result[0].text}")
            
            # 测试邮箱验证
            print("\n📧 Testing email validation:")
            email_result = await client.call_tool("validate_email", {
                "email": "<EMAIL>"
            })
            print(f"Email validation: {email_result[0].text}")
            
            # 6. 测试资源访问
            print("\n📖 Testing resource access...")
            
            # 获取服务器配置
            config_resource = await client.read_resource("config://server")
            print(f"Server config: {config_resource[0].text}")
            
            # 获取服务器统计
            stats_resource = await client.read_resource("data://stats")
            print(f"Server stats: {stats_resource[0].text}")
            
            # 获取工具帮助
            help_resource = await client.read_resource("help://tools/get_current_time")
            print(f"Tool help: {help_resource[0].text}")
            
            # 7. 测试提示模板
            print("\n💭 Testing prompt templates...")
            
            # 获取API集成指南
            guide_prompt = await client.get_prompt("api_integration_guide")
            print("API Integration Guide:")
            print(guide_prompt.messages[0].content.text[:200] + "...")
            
            print("\n✅ All tests completed successfully!")
            
        except Exception as e:
            print(f"❌ Error during testing: {e}")
            raise

async def interactive_client():
    """交互式客户端示例"""
    server_url = "http://127.0.0.1:8000/mcp/"
    
    print("🎮 Interactive MCP Client")
    print("Available commands:")
    print("  1. time - Get current time")
    print("  2. calc - Calculate sum of numbers")
    print("  3. random - Generate random numbers")
    print("  4. format - Format text")
    print("  5. email - Validate email")
    print("  6. config - Get server config")
    print("  7. quit - Exit")
    
    async with Client(server_url) as client:
        while True:
            try:
                command = input("\n🔤 Enter command (1-7): ").strip()
                
                if command == "1" or command.lower() == "time":
                    result = await client.call_tool("get_current_time")
                    print(f"⏰ Current time: {result[0].text}")
                
                elif command == "2" or command.lower() == "calc":
                    numbers_input = input("Enter numbers separated by commas: ")
                    numbers = [float(x.strip()) for x in numbers_input.split(",")]
                    result = await client.call_tool("calculate_sum", {"numbers": numbers})
                    print(f"🔢 Sum: {result[0].text}")
                
                elif command == "3" or command.lower() == "random":
                    count = int(input("How many random numbers? "))
                    result = await client.call_tool("generate_random_data", {"count": count})
                    print(f"🎲 Random numbers: {result[0].text}")
                
                elif command == "4" or command.lower() == "format":
                    text = input("Enter text to format: ")
                    style = input("Enter style (upper/lower/title/reverse): ")
                    result = await client.call_tool("format_text", {"text": text, "style": style})
                    print(f"📝 Formatted: {result[0].text}")
                
                elif command == "5" or command.lower() == "email":
                    email = input("Enter email to validate: ")
                    result = await client.call_tool("validate_email", {"email": email})
                    print(f"📧 Validation: {result[0].text}")
                
                elif command == "6" or command.lower() == "config":
                    result = await client.read_resource("config://server")
                    print(f"⚙️  Config: {result[0].text}")
                
                elif command == "7" or command.lower() == "quit":
                    print("👋 Goodbye!")
                    break
                
                else:
                    print("❌ Invalid command. Please try again.")
                    
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "interactive":
        print("Starting interactive mode...")
        asyncio.run(interactive_client())
    else:
        print("Running automated tests...")
        asyncio.run(test_mcp_server())

if __name__ == "__main__":
    main()
