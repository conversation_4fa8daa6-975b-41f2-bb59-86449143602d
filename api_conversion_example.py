#!/usr/bin/env python3
"""
API转换示例 - 展示如何将现有的本地API转换为MCP服务器

这个示例展示了几种常见的API转换模式：
1. REST API端点转换为MCP工具
2. 数据库操作转换
3. 文件系统操作转换
4. 外部服务集成
"""

import asyncio
import json
import os
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
import sqlite3
from fastmcp import FastMCP
import httpx

# 创建MCP服务器实例
mcp = FastMCP(
    name="API Conversion Example",
    description="示例：将本地API转换为MCP服务器"
)

# ============================================================================
# 1. REST API转换示例
# ============================================================================

# 原始REST API可能是这样的：
# @app.get("/api/users/{user_id}")
# def get_user(user_id: int):
#     return database.get_user(user_id)

@mcp.tool
def get_user_info(user_id: int) -> Dict[str, Any]:
    """获取用户信息 - 从REST API转换而来"""
    # 模拟数据库查询
    users_db = {
        1: {"id": 1, "name": "张三", "email": "<EMAIL>", "role": "admin"},
        2: {"id": 2, "name": "李四", "email": "<EMAIL>", "role": "user"},
        3: {"id": 3, "name": "王五", "email": "<EMAIL>", "role": "user"}
    }
    
    user = users_db.get(user_id)
    if user:
        return {"success": True, "user": user}
    else:
        return {"success": False, "error": "User not found"}

@mcp.tool
def create_user(name: str, email: str, role: str = "user") -> Dict[str, Any]:
    """创建新用户 - 从POST API转换而来"""
    # 模拟用户创建逻辑
    new_user = {
        "id": 999,  # 实际应用中应该是自动生成的ID
        "name": name,
        "email": email,
        "role": role,
        "created_at": datetime.now().isoformat()
    }
    
    return {"success": True, "user": new_user, "message": "User created successfully"}

# ============================================================================
# 2. 数据库操作转换示例
# ============================================================================

def init_sample_database():
    """初始化示例数据库"""
    db_path = "sample.db"
    if not os.path.exists(db_path):
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建表
        cursor.execute("""
            CREATE TABLE products (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                price REAL NOT NULL,
                category TEXT NOT NULL,
                stock INTEGER DEFAULT 0
            )
        """)
        
        # 插入示例数据
        sample_products = [
            (1, "笔记本电脑", 5999.99, "电子产品", 10),
            (2, "无线鼠标", 199.99, "电子产品", 50),
            (3, "办公椅", 899.99, "家具", 20),
            (4, "台灯", 299.99, "家具", 30)
        ]
        
        cursor.executemany(
            "INSERT INTO products (id, name, price, category, stock) VALUES (?, ?, ?, ?, ?)",
            sample_products
        )
        
        conn.commit()
        conn.close()

@mcp.tool
def query_products(category: Optional[str] = None, min_price: Optional[float] = None) -> List[Dict[str, Any]]:
    """查询产品 - 从数据库API转换而来"""
    init_sample_database()
    
    conn = sqlite3.connect("sample.db")
    cursor = conn.cursor()
    
    query = "SELECT * FROM products WHERE 1=1"
    params = []
    
    if category:
        query += " AND category = ?"
        params.append(category)
    
    if min_price is not None:
        query += " AND price >= ?"
        params.append(min_price)
    
    cursor.execute(query, params)
    rows = cursor.fetchall()
    
    # 转换为字典格式
    columns = [description[0] for description in cursor.description]
    products = [dict(zip(columns, row)) for row in rows]
    
    conn.close()
    
    return products

@mcp.tool
def update_product_stock(product_id: int, new_stock: int) -> Dict[str, Any]:
    """更新产品库存 - 从PUT API转换而来"""
    init_sample_database()
    
    conn = sqlite3.connect("sample.db")
    cursor = conn.cursor()
    
    cursor.execute(
        "UPDATE products SET stock = ? WHERE id = ?",
        (new_stock, product_id)
    )
    
    if cursor.rowcount > 0:
        conn.commit()
        result = {"success": True, "message": f"Product {product_id} stock updated to {new_stock}"}
    else:
        result = {"success": False, "error": "Product not found"}
    
    conn.close()
    return result

# ============================================================================
# 3. 文件系统操作转换示例
# ============================================================================

@mcp.tool
def list_files(directory: str = ".", file_extension: Optional[str] = None) -> List[Dict[str, Any]]:
    """列出文件 - 从文件管理API转换而来"""
    try:
        path = Path(directory)
        if not path.exists():
            return {"error": "Directory not found"}
        
        files = []
        for item in path.iterdir():
            if item.is_file():
                if file_extension is None or item.suffix == file_extension:
                    files.append({
                        "name": item.name,
                        "size": item.stat().st_size,
                        "modified": datetime.fromtimestamp(item.stat().st_mtime).isoformat(),
                        "extension": item.suffix
                    })
        
        return files
    
    except Exception as e:
        return {"error": str(e)}

@mcp.tool
def read_text_file(filepath: str, encoding: str = "utf-8") -> Dict[str, Any]:
    """读取文本文件 - 从文件读取API转换而来"""
    try:
        path = Path(filepath)
        if not path.exists():
            return {"success": False, "error": "File not found"}
        
        if not path.is_file():
            return {"success": False, "error": "Path is not a file"}
        
        content = path.read_text(encoding=encoding)
        
        return {
            "success": True,
            "filename": path.name,
            "content": content,
            "size": len(content),
            "lines": len(content.splitlines())
        }
    
    except Exception as e:
        return {"success": False, "error": str(e)}

@mcp.tool
def write_text_file(filepath: str, content: str, encoding: str = "utf-8") -> Dict[str, Any]:
    """写入文本文件 - 从文件写入API转换而来"""
    try:
        path = Path(filepath)
        path.parent.mkdir(parents=True, exist_ok=True)
        
        path.write_text(content, encoding=encoding)
        
        return {
            "success": True,
            "message": f"File written successfully: {filepath}",
            "size": len(content)
        }
    
    except Exception as e:
        return {"success": False, "error": str(e)}

# ============================================================================
# 4. 外部服务集成转换示例
# ============================================================================

@mcp.tool
async def get_weather(city: str, api_key: Optional[str] = None) -> Dict[str, Any]:
    """获取天气信息 - 从天气API转换而来"""
    # 这里使用模拟数据，实际应用中应该调用真实的天气API
    if not api_key:
        # 返回模拟数据
        weather_data = {
            "city": city,
            "temperature": 22,
            "humidity": 65,
            "description": "晴朗",
            "wind_speed": 5.2,
            "timestamp": datetime.now().isoformat(),
            "note": "This is simulated data. Provide api_key for real data."
        }
        return {"success": True, "weather": weather_data}
    
    # 实际API调用示例（需要真实的API密钥）
    try:
        async with httpx.AsyncClient() as client:
            # 这里应该是真实的天气API URL
            url = f"https://api.openweathermap.org/data/2.5/weather"
            params = {
                "q": city,
                "appid": api_key,
                "units": "metric",
                "lang": "zh_cn"
            }
            
            response = await client.get(url, params=params, timeout=10.0)
            response.raise_for_status()
            
            data = response.json()
            
            weather_info = {
                "city": data["name"],
                "temperature": data["main"]["temp"],
                "humidity": data["main"]["humidity"],
                "description": data["weather"][0]["description"],
                "wind_speed": data["wind"]["speed"],
                "timestamp": datetime.now().isoformat()
            }
            
            return {"success": True, "weather": weather_info}
            
    except Exception as e:
        return {"success": False, "error": f"Weather API error: {str(e)}"}

@mcp.tool
async def translate_text(text: str, target_language: str = "en", source_language: str = "auto") -> Dict[str, Any]:
    """翻译文本 - 从翻译API转换而来"""
    # 模拟翻译服务
    translations = {
        "你好": {"en": "Hello", "ja": "こんにちは", "ko": "안녕하세요"},
        "谢谢": {"en": "Thank you", "ja": "ありがとう", "ko": "감사합니다"},
        "再见": {"en": "Goodbye", "ja": "さようなら", "ko": "안녕히 가세요"}
    }
    
    if text in translations and target_language in translations[text]:
        translated = translations[text][target_language]
        return {
            "success": True,
            "original_text": text,
            "translated_text": translated,
            "source_language": "zh",
            "target_language": target_language,
            "note": "This is simulated translation data"
        }
    else:
        return {
            "success": False,
            "error": "Translation not available for this text/language pair",
            "note": "This is a demo with limited translations"
        }

# ============================================================================
# 资源定义 - 提供API文档和配置信息
# ============================================================================

@mcp.resource("api://documentation")
def get_api_documentation() -> Dict[str, Any]:
    """API文档资源"""
    return {
        "title": "API转换示例文档",
        "version": "1.0.0",
        "endpoints": {
            "user_management": {
                "get_user_info": "获取用户信息",
                "create_user": "创建新用户"
            },
            "product_management": {
                "query_products": "查询产品",
                "update_product_stock": "更新库存"
            },
            "file_operations": {
                "list_files": "列出文件",
                "read_text_file": "读取文件",
                "write_text_file": "写入文件"
            },
            "external_services": {
                "get_weather": "获取天气",
                "translate_text": "翻译文本"
            }
        },
        "last_updated": datetime.now().isoformat()
    }

@mcp.resource("config://database")
def get_database_config() -> Dict[str, Any]:
    """数据库配置信息"""
    return {
        "database_type": "SQLite",
        "database_file": "sample.db",
        "tables": ["products"],
        "connection_pool": "Not applicable for SQLite",
        "backup_enabled": False
    }

# ============================================================================
# 主函数
# ============================================================================

def main():
    """启动API转换示例服务器"""
    print("🔄 API Conversion Example Server")
    print("📡 Starting Streamable HTTP server...")
    print("🌐 Server URL: http://127.0.0.1:8001/mcp/")
    print("\n📚 Available APIs:")
    print("  👤 User Management: get_user_info, create_user")
    print("  📦 Product Management: query_products, update_product_stock")
    print("  📁 File Operations: list_files, read_text_file, write_text_file")
    print("  🌤️  External Services: get_weather, translate_text")
    
    # 初始化数据库
    init_sample_database()
    print("✅ Sample database initialized")
    
    # 启动服务器
    mcp.run(
        transport="http",
        host="127.0.0.1",
        port=8001,  # 使用不同的端口避免冲突
        path="/mcp",
        log_level="info"
    )

if __name__ == "__main__":
    main()
