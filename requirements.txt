# FastMCP Streamable HTTP Server Requirements

# Core FastMCP library
fastmcp>=2.0.0

# HTTP client for external API calls
httpx>=0.25.0

# Optional: For advanced async operations
aiofiles>=23.0.0

# Optional: For database operations
# sqlalchemy>=2.0.0
# asyncpg>=0.28.0

# Optional: For enhanced JSON handling
# orjson>=3.9.0

# Optional: For data validation
# pydantic>=2.0.0

# Development dependencies (uncomment if needed)
# pytest>=7.0.0
# pytest-asyncio>=0.21.0
# black>=23.0.0
# flake8>=6.0.0
