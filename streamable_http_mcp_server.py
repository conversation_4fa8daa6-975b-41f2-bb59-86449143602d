#!/usr/bin/env python3
"""
FastMCP Streamable HTTP Server Template
用于快速转换本地API接口为MCP Server的模板

这个模板展示了如何使用FastMCP创建一个Streamable HTTP模式的MCP服务器，
包含了常见的API转换模式和最佳实践。
"""

import asyncio
import json
import random
from datetime import datetime
from typing import Dict, List, Optional, Any
from fastmcp import FastMCP
import httpx

# 创建FastMCP实例
mcp = FastMCP(
    name="Streamable HTTP MCP Server",
    description="A template for converting local APIs to MCP servers using Streamable HTTP mode"
)

# ============================================================================
# 工具函数示例 - 将你的本地API逻辑转换为这些函数
# ============================================================================

@mcp.tool
def get_current_time() -> str:
    """获取当前时间 - 简单API示例"""
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

@mcp.tool
def calculate_sum(numbers: List[float]) -> float:
    """计算数字列表的总和 - 数学API示例"""
    return sum(numbers)

@mcp.tool
def generate_random_data(count: int = 5, min_val: int = 1, max_val: int = 100) -> List[int]:
    """生成随机数据 - 数据生成API示例"""
    return [random.randint(min_val, max_val) for _ in range(count)]

@mcp.tool
def format_text(text: str, style: str = "upper") -> str:
    """文本格式化 - 文本处理API示例
    
    Args:
        text: 要格式化的文本
        style: 格式化样式 (upper, lower, title, reverse)
    """
    if style == "upper":
        return text.upper()
    elif style == "lower":
        return text.lower()
    elif style == "title":
        return text.title()
    elif style == "reverse":
        return text[::-1]
    else:
        return text

@mcp.tool
async def fetch_external_data(url: str) -> Dict[str, Any]:
    """获取外部数据 - HTTP客户端API示例
    
    这展示了如何在MCP工具中调用外部API
    """
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(url, timeout=10.0)
            response.raise_for_status()
            
            # 尝试解析JSON，如果失败则返回文本
            try:
                return {"data": response.json(), "status": "success"}
            except json.JSONDecodeError:
                return {"data": response.text, "status": "success"}
                
    except httpx.RequestError as e:
        return {"error": f"Request failed: {str(e)}", "status": "error"}
    except httpx.HTTPStatusError as e:
        return {"error": f"HTTP error {e.response.status_code}", "status": "error"}

@mcp.tool
def validate_email(email: str) -> Dict[str, Any]:
    """邮箱验证 - 验证API示例"""
    import re
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    is_valid = bool(re.match(pattern, email))
    
    return {
        "email": email,
        "is_valid": is_valid,
        "message": "Valid email address" if is_valid else "Invalid email format"
    }

# ============================================================================
# 资源示例 - 提供静态或动态数据
# ============================================================================

@mcp.resource("config://server")
def get_server_config() -> Dict[str, Any]:
    """服务器配置信息"""
    return {
        "server_name": "Streamable HTTP MCP Server",
        "version": "1.0.0",
        "supported_formats": ["json", "text"],
        "max_request_size": "10MB",
        "timeout": "30s"
    }

@mcp.resource("data://stats")
def get_server_stats() -> Dict[str, Any]:
    """服务器统计信息"""
    return {
        "uptime": "Running",
        "requests_handled": random.randint(100, 1000),
        "last_updated": datetime.now().isoformat(),
        "status": "healthy"
    }

@mcp.resource("help://tools/{tool_name}")
def get_tool_help(tool_name: str) -> Dict[str, Any]:
    """获取特定工具的帮助信息 - 动态资源示例"""
    tool_docs = {
        "get_current_time": "Returns the current date and time in YYYY-MM-DD HH:MM:SS format",
        "calculate_sum": "Calculates the sum of a list of numbers",
        "generate_random_data": "Generates a list of random integers within specified range",
        "format_text": "Formats text according to specified style (upper, lower, title, reverse)",
        "fetch_external_data": "Fetches data from an external URL",
        "validate_email": "Validates email address format"
    }
    
    return {
        "tool_name": tool_name,
        "description": tool_docs.get(tool_name, "Tool not found"),
        "available": tool_name in tool_docs
    }

# ============================================================================
# 提示模板示例 - 为LLM提供结构化提示
# ============================================================================

@mcp.prompt
def api_integration_guide() -> str:
    """API集成指南提示模板"""
    return """
# API集成指南

这个MCP服务器提供了以下API功能：

## 可用工具：
1. **get_current_time** - 获取当前时间
2. **calculate_sum** - 计算数字总和
3. **generate_random_data** - 生成随机数据
4. **format_text** - 文本格式化
5. **fetch_external_data** - 获取外部数据
6. **validate_email** - 邮箱验证

## 使用建议：
- 对于时间相关的查询，使用 get_current_time
- 对于数学计算，使用 calculate_sum
- 对于需要随机数据的场景，使用 generate_random_data
- 对于文本处理，使用 format_text
- 对于外部API调用，使用 fetch_external_data
- 对于数据验证，使用 validate_email

请根据用户需求选择合适的工具。
"""

@mcp.prompt
def error_handling_guide() -> str:
    """错误处理指南"""
    return """
# 错误处理最佳实践

当使用这个MCP服务器时，请注意：

1. **网络请求** - fetch_external_data 可能因网络问题失败
2. **数据验证** - 确保输入数据格式正确
3. **超时处理** - 长时间运行的操作可能超时
4. **资源限制** - 避免请求过大的数据集

建议在调用工具前先检查输入参数的有效性。
"""

# ============================================================================
# 服务器启动配置
# ============================================================================

def main():
    """主函数 - 配置并启动Streamable HTTP服务器"""
    print("🚀 Starting FastMCP Streamable HTTP Server...")
    print("📡 Server will be available at: http://127.0.0.1:8000/mcp/")
    print("🔧 Use this URL to connect MCP clients")
    print("📚 Available tools:", len([tool for tool in dir(mcp) if not tool.startswith('_')]))
    
    # 启动Streamable HTTP服务器
    mcp.run(
        transport="http",           # 使用Streamable HTTP传输
        host="127.0.0.1",          # 绑定到本地主机
        port=8000,                 # 端口号
        path="/mcp",               # MCP端点路径
        log_level="info"           # 日志级别
    )

if __name__ == "__main__":
    main()
