#!/usr/bin/env python3
"""
Elasticsearch MCP客户端使用示例
演示如何使用Python客户端连接和操作Elasticsearch MCP服务器
"""

import asyncio
import json
from simple_es_mcp_client import SimpleElasticsearchMCPClient

async def basic_example():
    """基本使用示例"""
    print("🚀 Elasticsearch MCP客户端基本使用示例")
    print("=" * 60)
    
    # 创建客户端
    client = SimpleElasticsearchMCPClient()
    
    try:
        # 连接到服务器
        print("🔗 连接到Elasticsearch MCP服务器...")
        if not await client.connect():
            print("❌ 连接失败，请检查配置和网络")
            return
        
        print("✅ 连接成功!")
        
        # 1. 获取可用工具
        print("\n📋 获取可用工具...")
        tools = await client.get_tools()
        print(f"发现 {len(tools)} 个工具:")
        for tool in tools:
            print(f"  - {tool['name']}: {tool['description']}")
        
        # 2. 列出索引
        print("\n📊 列出所有索引...")
        indices_result = await client.list_indices("*")
        if indices_result["success"]:
            print("✅ 索引列表获取成功")
            # 解析结果
            try:
                indices_data = json.loads(indices_result["result"])
                if isinstance(indices_data, list):
                    print(f"找到 {len(indices_data)} 个索引:")
                    for idx in indices_data[:5]:  # 只显示前5个
                        print(f"  - {idx}")
                    if len(indices_data) > 5:
                        print(f"  ... 还有 {len(indices_data) - 5} 个索引")
                else:
                    print(f"索引信息: {indices_data}")
            except:
                print(f"原始结果: {indices_result['result']}")
        else:
            print(f"❌ 获取索引失败: {indices_result['error']}")
        
        # 3. 获取分片信息
        print("\n🔧 获取分片信息...")
        shards_result = await client.get_shards()
        if shards_result["success"]:
            print("✅ 分片信息获取成功")
            print(f"分片信息: {shards_result['result'][:200]}...")  # 只显示前200字符
        else:
            print(f"❌ 获取分片信息失败: {shards_result['error']}")
        
        # 4. 执行搜索
        print("\n🔍 执行搜索查询...")
        search_query = {
            "query": {
                "match_all": {}
            }
        }
        
        search_result = await client.search("*", search_query, 3)
        if search_result["success"]:
            print("✅ 搜索成功")
            print(f"搜索结果: {search_result['result'][:300]}...")  # 只显示前300字符
        else:
            print(f"❌ 搜索失败: {search_result['error']}")
        
        print("\n🎉 基本示例完成!")
        
    except Exception as e:
        print(f"❌ 示例执行出错: {e}")
    
    finally:
        await client.disconnect()

async def advanced_search_example():
    """高级搜索示例"""
    print("\n🔍 高级搜索示例")
    print("=" * 40)
    
    client = SimpleElasticsearchMCPClient()
    
    try:
        if not await client.connect():
            return
        
        # 不同类型的搜索查询
        search_queries = [
            {
                "name": "匹配所有文档",
                "query": {
                    "query": {
                        "match_all": {}
                    }
                }
            },
            {
                "name": "范围查询",
                "query": {
                    "query": {
                        "range": {
                            "@timestamp": {
                                "gte": "now-1d"
                            }
                        }
                    }
                }
            },
            {
                "name": "布尔查询",
                "query": {
                    "query": {
                        "bool": {
                            "must": [
                                {"match_all": {}}
                            ]
                        }
                    }
                }
            }
        ]
        
        for search_example in search_queries:
            print(f"\n📝 {search_example['name']}:")
            result = await client.search("*", search_example["query"], 1)
            
            if result["success"]:
                print("✅ 查询成功")
                print(f"结果预览: {result['result'][:150]}...")
            else:
                print(f"❌ 查询失败: {result['error']}")
        
    except Exception as e:
        print(f"❌ 高级搜索示例出错: {e}")
    
    finally:
        await client.disconnect()

async def index_management_example():
    """索引管理示例"""
    print("\n📊 索引管理示例")
    print("=" * 40)
    
    client = SimpleElasticsearchMCPClient()
    
    try:
        if not await client.connect():
            return
        
        # 1. 列出特定模式的索引
        print("📋 列出日志索引...")
        log_indices = await client.list_indices("log*")
        if log_indices["success"]:
            print("✅ 日志索引获取成功")
            print(f"结果: {log_indices['result']}")
        
        # 2. 获取索引映射
        print("\n🗺️ 获取索引映射...")
        # 先获取一个索引名
        all_indices = await client.list_indices("*")
        if all_indices["success"]:
            try:
                indices_data = json.loads(all_indices["result"])
                if isinstance(indices_data, list) and indices_data:
                    first_index = indices_data[0]
                    print(f"获取索引 '{first_index}' 的映射...")
                    
                    mapping_result = await client.get_mappings(first_index)
                    if mapping_result["success"]:
                        print("✅ 映射获取成功")
                        print(f"映射信息: {mapping_result['result'][:200]}...")
                    else:
                        print(f"❌ 获取映射失败: {mapping_result['error']}")
                else:
                    print("⚠️ 没有找到可用的索引")
            except:
                print("⚠️ 无法解析索引列表")
        
    except Exception as e:
        print(f"❌ 索引管理示例出错: {e}")
    
    finally:
        await client.disconnect()

async def custom_tool_example():
    """自定义工具调用示例"""
    print("\n🛠️ 自定义工具调用示例")
    print("=" * 40)
    
    client = SimpleElasticsearchMCPClient()
    
    try:
        if not await client.connect():
            return
        
        # 获取所有工具
        tools = await client.get_tools()
        
        print("可用工具:")
        for i, tool in enumerate(tools, 1):
            print(f"  {i}. {tool['name']} - {tool['description']}")
        
        # 示例：直接调用工具
        print("\n🔧 直接调用 list_indices 工具...")
        result = await client.call_tool("list_indices", {"indexPattern": "*"})
        
        if result["success"]:
            print("✅ 工具调用成功")
            print(f"结果: {result['result']}")
        else:
            print(f"❌ 工具调用失败: {result['error']}")
        
    except Exception as e:
        print(f"❌ 自定义工具示例出错: {e}")
    
    finally:
        await client.disconnect()

async def main():
    """主函数 - 运行所有示例"""
    print("🚀 Elasticsearch MCP客户端完整示例")
    print("=" * 60)
    
    # 运行所有示例
    await basic_example()
    await advanced_search_example()
    await index_management_example()
    await custom_tool_example()
    
    print("\n🎉 所有示例完成!")
    print("\n💡 提示:")
    print("  - 如果遇到网络错误，请检查Elasticsearch服务器地址")
    print("  - 如果遇到认证错误，请检查用户名和密码")
    print("  - 更多用法请参考 simple_es_mcp_client.py 的交互式模式")

if __name__ == "__main__":
    asyncio.run(main())
