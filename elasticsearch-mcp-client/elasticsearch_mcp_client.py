#!/usr/bin/env python3
"""
Elasticsearch MCP客户端
连接到Elasticsearch MCP服务器并提供交互式功能
"""

import asyncio
import json
import sys
from typing import Dict, Any, List, Optional
from fastmcp import Client
from fastmcp.client.transports import NpxStdioTransport

class ElasticsearchMCPClient:
    """Elasticsearch MCP客户端类"""
    
    def __init__(self, es_url: str, es_username: str, es_password: str, es_version: str = "6"):
        """
        初始化Elasticsearch MCP客户端
        
        Args:
            es_url: Elasticsearch服务器URL
            es_username: Elasticsearch用户名
            es_password: Elasticsearch密码
            es_version: Elasticsearch版本
        """
        self.es_url = es_url
        self.es_username = es_username
        self.es_password = es_password
        self.es_version = es_version
        
        # 创建NpxStdioTransport
        self.transport = NpxStdioTransport(
            package="@elastic/mcp-server-elasticsearch",
            args=["-y"]  # 自动确认安装
        )
        
        # 设置环境变量
        self.transport.env = {
            "ES_URL": self.es_url,
            "ES_USERNAME": self.es_username,
            "ES_PASSWORD": self.es_password,
            "ES_VERSION": self.es_version
        }
        
        # 创建客户端
        self.client = Client(self.transport)
        
    async def connect(self):
        """连接到MCP服务器"""
        try:
            print("🔗 正在连接到Elasticsearch MCP服务器...")
            await self.client.__aenter__()
            
            # 测试连接
            await self.client.ping()
            print("✅ 成功连接到Elasticsearch MCP服务器!")
            
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    async def disconnect(self):
        """断开连接"""
        try:
            await self.client.__aexit__(None, None, None)
            print("🔌 已断开连接")
        except Exception as e:
            print(f"⚠️ 断开连接时出错: {e}")
    
    async def list_tools(self) -> List[Dict[str, Any]]:
        """列出可用的工具"""
        try:
            tools = await self.client.list_tools()
            print(f"\n📋 可用工具 ({len(tools)}个):")
            print("=" * 50)
            
            tool_list = []
            for i, tool in enumerate(tools, 1):
                tool_info = {
                    "name": tool.name,
                    "description": tool.description
                }
                tool_list.append(tool_info)
                print(f"{i:2d}. {tool.name}")
                print(f"    描述: {tool.description}")
                print()
            
            return tool_list
        except Exception as e:
            print(f"❌ 获取工具列表失败: {e}")
            return []
    
    async def list_resources(self) -> List[Dict[str, Any]]:
        """列出可用的资源"""
        try:
            resources = await self.client.list_resources()
            print(f"\n📚 可用资源 ({len(resources)}个):")
            print("=" * 50)
            
            resource_list = []
            for i, resource in enumerate(resources, 1):
                resource_info = {
                    "uri": resource.uri,
                    "name": resource.name,
                    "description": getattr(resource, 'description', 'N/A')
                }
                resource_list.append(resource_info)
                print(f"{i:2d}. {resource.uri}")
                print(f"    名称: {resource.name}")
                if hasattr(resource, 'description'):
                    print(f"    描述: {resource.description}")
                print()
            
            return resource_list
        except Exception as e:
            print(f"❌ 获取资源列表失败: {e}")
            return []
    
    async def search_documents(self, index: str, query: str, size: int = 10) -> Dict[str, Any]:
        """搜索文档"""
        try:
            print(f"\n🔍 在索引 '{index}' 中搜索: {query}")
            
            # 调用搜索工具 (根据实际的工具名称调整)
            result = await self.client.call_tool("search", {
                "index": index,
                "query": query,
                "size": size
            })
            
            print("✅ 搜索完成!")
            return {"success": True, "result": result[0].text}
            
        except Exception as e:
            print(f"❌ 搜索失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_index_info(self, index: str) -> Dict[str, Any]:
        """获取索引信息"""
        try:
            print(f"\n📊 获取索引 '{index}' 的信息...")
            
            # 调用获取索引信息的工具
            result = await self.client.call_tool("get_index_info", {
                "index": index
            })
            
            print("✅ 获取索引信息完成!")
            return {"success": True, "result": result[0].text}
            
        except Exception as e:
            print(f"❌ 获取索引信息失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def interactive_mode(self):
        """交互式模式"""
        print("\n🎯 进入交互式模式")
        print("可用命令:")
        print("  1. list-tools    - 列出所有工具")
        print("  2. list-resources - 列出所有资源")
        print("  3. search        - 搜索文档")
        print("  4. index-info    - 获取索引信息")
        print("  5. call-tool     - 调用自定义工具")
        print("  6. quit          - 退出")
        print("=" * 50)
        
        while True:
            try:
                command = input("\n请输入命令 (输入数字或命令名): ").strip().lower()
                
                if command in ['6', 'quit', 'exit', 'q']:
                    break
                elif command in ['1', 'list-tools']:
                    await self.list_tools()
                elif command in ['2', 'list-resources']:
                    await self.list_resources()
                elif command in ['3', 'search']:
                    index = input("请输入索引名: ").strip()
                    query = input("请输入搜索查询: ").strip()
                    size = input("请输入返回结果数量 (默认10): ").strip()
                    size = int(size) if size.isdigit() else 10
                    
                    result = await self.search_documents(index, query, size)
                    print(f"\n搜索结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
                elif command in ['4', 'index-info']:
                    index = input("请输入索引名: ").strip()
                    result = await self.get_index_info(index)
                    print(f"\n索引信息: {json.dumps(result, indent=2, ensure_ascii=False)}")
                elif command in ['5', 'call-tool']:
                    await self.custom_tool_call()
                else:
                    print("❌ 未知命令，请重新输入")
                    
            except KeyboardInterrupt:
                print("\n\n👋 用户中断，退出交互模式")
                break
            except Exception as e:
                print(f"❌ 执行命令时出错: {e}")
    
    async def custom_tool_call(self):
        """自定义工具调用"""
        try:
            # 先列出可用工具
            tools = await self.client.list_tools()
            print("\n可用工具:")
            for i, tool in enumerate(tools, 1):
                print(f"{i}. {tool.name} - {tool.description}")
            
            tool_name = input("\n请输入工具名称: ").strip()
            
            # 获取参数
            print("请输入工具参数 (JSON格式，例如: {\"index\": \"my-index\", \"query\": \"test\"})")
            params_str = input("参数: ").strip()
            
            if params_str:
                params = json.loads(params_str)
            else:
                params = {}
            
            # 调用工具
            result = await self.client.call_tool(tool_name, params)
            print(f"\n工具调用结果:")
            print(json.dumps(result[0].text, indent=2, ensure_ascii=False))
            
        except json.JSONDecodeError:
            print("❌ 参数格式错误，请使用有效的JSON格式")
        except Exception as e:
            print(f"❌ 调用工具失败: {e}")

async def main():
    """主函数"""
    print("🚀 Elasticsearch MCP客户端")
    print("=" * 50)
    
    # 从你的配置中获取连接信息
    es_url = "https://medusa.ioq.com:19203"
    es_username = "elastic"
    es_password = "e$search"
    es_version = "6"
    
    # 创建客户端
    client = ElasticsearchMCPClient(
        es_url=es_url,
        es_username=es_username,
        es_password=es_password,
        es_version=es_version
    )
    
    try:
        # 连接到服务器
        if await client.connect():
            # 显示基本信息
            await client.list_tools()
            await client.list_resources()
            
            # 进入交互式模式
            await client.interactive_mode()
        
    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
    finally:
        # 断开连接
        await client.disconnect()

if __name__ == "__main__":
    asyncio.run(main())
