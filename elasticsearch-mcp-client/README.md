# Elasticsearch MCP Python客户端

这是一个用于连接Elasticsearch MCP服务器的Python客户端集合，使用FastMCP SDK构建，与你在Cursor中配置的Elasticsearch MCP服务器完全兼容。

## 📁 项目结构

```
elasticsearch-mcp-client/
├── README.md                    # 本文件
├── ELASTICSEARCH_MCP_README.md  # 详细文档
├── elasticsearch_config.py      # 配置管理
├── simple_es_mcp_client.py     # 简化客户端 (推荐)
├── elasticsearch_mcp_client.py  # 完整客户端
├── test_elasticsearch_mcp.py    # 连接测试脚本
└── example_usage.py            # 使用示例
```

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装FastMCP Python SDK
pip install fastmcp

# 确保已安装Elasticsearch MCP服务器 (如果还没有)
npm install -g @elastic/mcp-server-elasticsearch
```

### 2. 配置检查

你的Elasticsearch配置已经在 `elasticsearch_config.py` 中设置好了：

```python
{
    "ES_URL": "https://medusa.iotxmm.com:19203",
    "ES_USERNAME": "elastic",
    "ES_PASSWORD": "elastic@remotelog2020",
    "ES_VERSION": "6"
}
```

### 3. 快速测试

```bash
# 测试连接
python test_elasticsearch_mcp.py

# 运行演示
python simple_es_mcp_client.py

# 查看使用示例
python example_usage.py
```

## 🎯 主要功能

### ✅ 已实现的功能

1. **连接管理** - 自动连接到Elasticsearch MCP服务器
2. **索引操作** - 列出索引、获取映射、分片信息
3. **搜索功能** - 支持各种Elasticsearch查询DSL
4. **工具发现** - 自动发现可用的MCP工具
5. **错误处理** - 完善的错误处理和调试信息

### 🛠️ 可用工具

根据你的Elasticsearch MCP服务器，包含以下工具：

1. **list_indices** - 列出所有可用的Elasticsearch索引
2. **get_mappings** - 获取特定索引的字段映射
3. **search** - 使用查询DSL执行Elasticsearch搜索
4. **get_shards** - 获取所有或特定索引的分片信息

## 📖 使用示例

### 基本使用

```python
import asyncio
from simple_es_mcp_client import SimpleElasticsearchMCPClient

async def basic_usage():
    client = SimpleElasticsearchMCPClient()
    
    # 连接
    if await client.connect():
        # 列出索引
        indices = await client.list_indices("*")
        print(f"索引: {indices}")
        
        # 搜索
        query = {"query": {"match_all": {}}}
        results = await client.search("*", query, 10)
        print(f"搜索结果: {results}")
        
        await client.disconnect()

asyncio.run(basic_usage())
```

### 交互式模式

```bash
python simple_es_mcp_client.py
# 选择: 2 (交互式模式)
```

可用命令：
- `list-tools` - 列出所有工具
- `search` - 搜索文档
- `list-indices` - 列出索引
- `get-mappings` - 获取索引映射
- `get-shards` - 获取分片信息
- `custom-call` - 自定义工具调用

## 🔧 配置说明

### 环境变量覆盖

你可以通过环境变量覆盖默认配置：

```bash
export ES_URL="https://your-elasticsearch-url:9200"
export ES_USERNAME="your-username"
export ES_PASSWORD="your-password"
export ES_VERSION="8"

python simple_es_mcp_client.py
```

### 配置验证

```python
from elasticsearch_config import validate_config, print_config

# 查看当前配置
print_config()

# 验证配置
if validate_config():
    print("配置有效")
```

## 🔍 故障排除

### 常见问题

1. **连接失败**
   ```
   ❌ 连接失败: getaddrinfo ENOTFOUND medusa.iotxmm.com
   ```
   - 检查网络连接和服务器地址
   - 确认服务器可访问

2. **认证失败**
   ```
   ❌ Authentication failed
   ```
   - 检查用户名和密码
   - 确认用户权限

3. **工具调用失败**
   ```
   ❌ Invalid arguments for tool
   ```
   - 检查参数格式
   - 参考工具文档

### 调试模式

```bash
# 启用详细输出
python test_elasticsearch_mcp.py
# 选择: 1 (完整测试)
```

## 📝 开发说明

### 扩展客户端

```python
from simple_es_mcp_client import SimpleElasticsearchMCPClient

class MyCustomClient(SimpleElasticsearchMCPClient):
    async def my_search(self, index: str, term: str):
        """自定义搜索方法"""
        query = {
            "query": {
                "match": {
                    "_all": term
                }
            }
        }
        return await self.search(index, query, 10)
```

### 批量操作

```python
async def batch_search():
    client = SimpleElasticsearchMCPClient()
    await client.connect()
    
    queries = [
        {"match_all": {}},
        {"range": {"@timestamp": {"gte": "now-1d"}}},
        {"bool": {"must": [{"match_all": {}}]}}
    ]
    
    results = []
    for query in queries:
        result = await client.search("*", {"query": query}, 5)
        results.append(result)
    
    await client.disconnect()
    return results
```

## 🔗 相关链接

- [FastMCP文档](https://github.com/jlowin/fastmcp)
- [Elasticsearch MCP服务器](https://github.com/elastic/mcp-server-elasticsearch)
- [MCP协议规范](https://modelcontextprotocol.io/)

## 📄 许可证

本项目遵循MIT许可证。

---

## 🎉 开始使用

1. 确保依赖已安装
2. 运行 `python test_elasticsearch_mcp.py` 测试连接
3. 运行 `python simple_es_mcp_client.py` 进入交互模式
4. 查看 `example_usage.py` 了解更多用法

祝你使用愉快！🚀
