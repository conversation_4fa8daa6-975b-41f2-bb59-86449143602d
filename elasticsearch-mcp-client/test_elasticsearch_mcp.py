#!/usr/bin/env python3
"""
Elasticsearch MCP客户端测试脚本
快速测试连接到Elasticsearch MCP服务器
"""

import asyncio
import json
import os
from fastmcp import Client
from fastmcp.client.transports import NpxStdioTransport

async def test_elasticsearch_mcp():
    """测试Elasticsearch MCP连接"""
    print("🧪 测试Elasticsearch MCP服务器连接")
    print("=" * 60)
    
    # 配置信息 (与你的cursor配置一致)
    config = {
        "ES_URL": "https://medusa.ioq.com:19203",
        "ES_USERNAME": "elastic", 
        "ES_PASSWORD": "e$search",
        "ES_VERSION": "6"
    }
    
    print("📋 连接配置:")
    print(f"  ES_URL: {config['ES_URL']}")
    print(f"  ES_USERNAME: {config['ES_USERNAME']}")
    print(f"  ES_PASSWORD: {'*' * len(config['ES_PASSWORD'])}")
    print(f"  ES_VERSION: {config['ES_VERSION']}")
    print()
    
    try:
        # 创建NpxStdioTransport
        print("🚀 创建NPX传输...")
        transport = NpxStdioTransport(
            package="@elastic/mcp-server-elasticsearch",
            args=["-y"]  # 自动确认
        )
        
        # 设置环境变量
        transport.env = config
        
        # 创建客户端
        client = Client(transport)
        
        print("🔗 正在连接到Elasticsearch MCP服务器...")
        
        async with client:
            # 测试连接
            print("📡 测试ping连接...")
            await client.ping()
            print("✅ Ping成功!")
            
            # 获取工具列表
            print("\n📋 获取可用工具...")
            tools = await client.list_tools()
            print(f"✅ 发现 {len(tools)} 个工具:")
            
            for i, tool in enumerate(tools, 1):
                print(f"  {i:2d}. {tool.name}")
                print(f"      描述: {tool.description}")
                if hasattr(tool, 'inputSchema') and tool.inputSchema:
                    print(f"      参数: {tool.inputSchema.get('properties', {}).keys()}")
                print()
            
            # 获取资源列表
            print("📚 获取可用资源...")
            try:
                resources = await client.list_resources()
                print(f"✅ 发现 {len(resources)} 个资源:")
                
                for i, resource in enumerate(resources, 1):
                    print(f"  {i:2d}. {resource.uri}")
                    print(f"      名称: {resource.name}")
                    if hasattr(resource, 'description'):
                        print(f"      描述: {resource.description}")
                    print()
            except Exception as e:
                print(f"⚠️ 获取资源列表失败: {e}")
            
            # 测试一个简单的工具调用 (如果有的话)
            print("🛠️ 测试工具调用...")
            if tools:
                # 尝试调用第一个工具 (通常是健康检查或简单查询)
                first_tool = tools[0]
                print(f"尝试调用工具: {first_tool.name}")
                
                try:
                    # 根据工具类型调用不同的参数
                    if "health" in first_tool.name.lower():
                        result = await client.call_tool(first_tool.name, {})
                    elif "search" in first_tool.name.lower():
                        # 简单搜索测试
                        result = await client.call_tool(first_tool.name, {
                            "index": "_all",
                            "query": "*",
                            "size": 1
                        })
                    elif "list" in first_tool.name.lower() or "get" in first_tool.name.lower():
                        result = await client.call_tool(first_tool.name, {})
                    else:
                        print(f"⚠️ 跳过工具 {first_tool.name} (需要特定参数)")
                        result = None
                    
                    if result:
                        print(f"✅ 工具调用成功!")
                        print(f"结果类型: {type(result[0])}")
                        if hasattr(result[0], 'text'):
                            result_text = result[0].text
                            if len(result_text) > 200:
                                print(f"结果预览: {result_text[:200]}...")
                            else:
                                print(f"结果: {result_text}")
                        
                except Exception as e:
                    print(f"⚠️ 工具调用失败: {e}")
            
            print("\n🎉 所有测试完成!")
            return True
            
    except Exception as e:
        print(f"❌ 连接或测试失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        
        # 提供一些调试信息
        print("\n🔧 调试信息:")
        print("1. 确保已安装 @elastic/mcp-server-elasticsearch:")
        print("   npm install -g @elastic/mcp-server-elasticsearch")
        print("2. 检查Elasticsearch服务器是否可访问")
        print("3. 验证用户名和密码是否正确")
        print("4. 检查网络连接")
        
        return False

async def quick_tool_test():
    """快速工具测试"""
    print("\n🔧 快速工具测试模式")
    print("=" * 40)
    
    config = {
        "ES_URL": "https://medusa.ioq.com:19203",
        "ES_USERNAME": "elastic", 
        "ES_PASSWORD": "e$search",
        "ES_VERSION": "6"
    }
    
    transport = NpxStdioTransport(
        package="@elastic/mcp-server-elasticsearch",
        args=["-y"]
    )
    transport.env = config
    
    client = Client(transport)
    
    try:
        async with client:
            tools = await client.list_tools()
            
            print("请选择要测试的工具:")
            for i, tool in enumerate(tools, 1):
                print(f"{i}. {tool.name} - {tool.description}")
            
            choice = input("\n请输入工具编号: ").strip()
            
            if choice.isdigit() and 1 <= int(choice) <= len(tools):
                selected_tool = tools[int(choice) - 1]
                print(f"\n测试工具: {selected_tool.name}")
                
                # 获取参数
                params_str = input("请输入参数 (JSON格式，留空使用默认): ").strip()
                
                if params_str:
                    params = json.loads(params_str)
                else:
                    params = {}
                
                result = await client.call_tool(selected_tool.name, params)
                print(f"\n✅ 调用成功!")
                print(f"结果: {result[0].text}")
            else:
                print("❌ 无效选择")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    """主函数"""
    print("🚀 Elasticsearch MCP客户端测试工具")
    print("=" * 50)
    
    mode = input("选择模式:\n1. 完整测试\n2. 快速工具测试\n请输入 (1/2): ").strip()
    
    if mode == "2":
        asyncio.run(quick_tool_test())
    else:
        asyncio.run(test_elasticsearch_mcp())

if __name__ == "__main__":
    main()
